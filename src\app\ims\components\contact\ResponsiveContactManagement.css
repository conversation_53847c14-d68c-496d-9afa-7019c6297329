/* 響應式聯絡人管理樣式 */

/* 基礎響應式容器 */
.responsive-contact-management-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 移動端優化 */
@media (max-width: 768px) {
  .responsive-contact-management-page {
    padding: 8px;
  }
  
  .responsive-contact-management-page .ant-page-header {
    padding: 12px 8px;
  }
  
  .responsive-contact-management-page .ant-page-header-heading-title {
    font-size: 18px;
  }
  
  .responsive-contact-management-page .ant-page-header-heading-sub-title {
    font-size: 12px;
  }
  
  .responsive-contact-management-page .ant-card {
    margin-bottom: 8px;
  }
  
  .responsive-contact-management-page .ant-card-head {
    padding: 8px 12px;
  }
  
  .responsive-contact-management-page .ant-card-body {
    padding: 12px;
  }
}

/* 平板端優化 */
@media (min-width: 769px) and (max-width: 1024px) {
  .responsive-contact-management-page {
    padding: 16px;
  }
  
  .responsive-contact-management-page .ant-page-header {
    padding: 16px 0;
  }
  
  .responsive-contact-management-page .ant-card {
    margin-bottom: 16px;
  }
}

/* 桌面端優化 */
@media (min-width: 1025px) {
  .responsive-contact-management-page {
    padding: 24px;
  }
  
  .responsive-contact-management-page .ant-page-header {
    padding: 24px 0;
  }
  
  .responsive-contact-management-page .ant-card {
    margin-bottom: 24px;
  }
}

/* 聯絡人表格響應式樣式 */
.responsive-contact-table {
  width: 100%;
}

.responsive-contact-table.mobile .ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.responsive-contact-table.mobile .ant-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.responsive-contact-table.mobile .ant-card-head {
  border-bottom: 1px solid #f0f0f0;
  padding: 8px 12px;
}

.responsive-contact-table.mobile .ant-card-body {
  padding: 12px;
}

.responsive-contact-table.mobile .ant-avatar {
  border: 2px solid #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.responsive-contact-table.desktop .ant-table {
  border-radius: 8px;
  overflow: hidden;
}

.responsive-contact-table.desktop .ant-table-thead > tr > th {
  background-color: #fafafa;
  font-weight: 600;
  color: #262626;
}

.responsive-contact-table.desktop .ant-table-tbody > tr:hover > td {
  background-color: #f5f5f5;
}

/* 聯絡人表單響應式樣式 */
.responsive-contact-form-modal .ant-modal {
  margin: 0;
}

.responsive-contact-form-modal .ant-modal-content {
  border-radius: 8px;
}

.responsive-contact-form-modal .ant-modal-header {
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 24px;
}

.responsive-contact-form-modal .ant-modal-body {
  padding: 24px;
}

.responsive-contact-form-modal .ant-modal-footer {
  border-top: 1px solid #f0f0f0;
  padding: 12px 24px;
}

@media (max-width: 768px) {
  .responsive-contact-form-modal .ant-modal {
    top: 10px;
    margin: 0 8px;
  }
  
  .responsive-contact-form-modal .ant-modal-content {
    border-radius: 12px;
  }
  
  .responsive-contact-form-modal .ant-modal-header {
    padding: 12px 16px;
  }
  
  .responsive-contact-form-modal .ant-modal-body {
    padding: 16px;
  }
  
  .responsive-contact-form-modal .ant-modal-footer {
    padding: 8px 16px;
  }
  
  .responsive-contact-form-modal .ant-form-item-label {
    padding-bottom: 4px;
  }
  
  .responsive-contact-form-modal .ant-form-item {
    margin-bottom: 16px;
  }
}

/* 搜尋和篩選容器響應式樣式 */
.responsive-filter-search {
  width: 100%;
}

.responsive-filter-search.mobile .ant-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.responsive-filter-search.mobile .ant-input {
  border-radius: 6px;
}

.responsive-filter-search.mobile .ant-btn {
  border-radius: 6px;
}

.responsive-filter-search.desktop .ant-card {
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.responsive-filter-search.desktop .ant-input {
  border-radius: 6px;
}

.responsive-filter-search.desktop .ant-btn {
  border-radius: 6px;
}

/* 聯絡人管理中心響應式樣式 */
.responsive-contact-management {
  width: 100%;
}

.responsive-contact-management .ant-card {
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.responsive-contact-management .ant-card-head {
  border-bottom: 1px solid #f0f0f0;
}

.responsive-contact-management .ant-card-body {
  padding: 16px;
}

@media (max-width: 768px) {
  .responsive-contact-management .ant-card-body {
    padding: 12px;
  }
  
  .responsive-contact-management .ant-space {
    width: 100%;
  }
  
  .responsive-contact-management .ant-space-item {
    flex: 1;
  }
}

/* 統計卡片響應式樣式 */
.ant-statistic {
  text-align: center;
}

.ant-statistic-title {
  font-size: 12px;
  color: #8c8c8c;
  margin-bottom: 4px;
}

.ant-statistic-content {
  font-size: 20px;
  font-weight: 600;
  color: #262626;
}

@media (max-width: 768px) {
  .ant-statistic-title {
    font-size: 10px;
  }
  
  .ant-statistic-content {
    font-size: 16px;
  }
}

/* 標籤頁響應式樣式 */
.ant-tabs {
  width: 100%;
}

.ant-tabs-tab {
  padding: 8px 16px;
}

.ant-tabs-tab-btn {
  font-size: 14px;
}

@media (max-width: 768px) {
  .ant-tabs-tab {
    padding: 6px 12px;
  }
  
  .ant-tabs-tab-btn {
    font-size: 12px;
  }
  
  .ant-tabs-content-holder {
    padding: 8px 0;
  }
}

/* 按鈕響應式樣式 */
.ant-btn {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.ant-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.ant-btn-primary {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  border: none;
}

.ant-btn-primary:hover {
  background: linear-gradient(135deg, #40a9ff 0%, #69c0ff 100%);
}

@media (max-width: 768px) {
  .ant-btn {
    font-size: 12px;
    padding: 4px 8px;
    height: 32px;
  }
  
  .ant-btn-lg {
    height: 36px;
    padding: 6px 12px;
  }
}

/* 抽屜響應式樣式 */
.ant-drawer {
  border-radius: 12px 12px 0 0;
}

.ant-drawer-header {
  border-bottom: 1px solid #f0f0f0;
  padding: 16px 20px;
}

.ant-drawer-body {
  padding: 20px;
}

@media (max-width: 768px) {
  .ant-drawer-header {
    padding: 12px 16px;
  }
  
  .ant-drawer-body {
    padding: 16px;
  }
}

/* 步驟條響應式樣式 */
.ant-steps {
  margin-bottom: 24px;
}

.ant-steps-item-title {
  font-size: 14px;
  font-weight: 500;
}

.ant-steps-item-description {
  font-size: 12px;
  color: #8c8c8c;
}

@media (max-width: 768px) {
  .ant-steps {
    margin-bottom: 16px;
  }
  
  .ant-steps-item-title {
    font-size: 12px;
  }
  
  .ant-steps-item-description {
    font-size: 10px;
  }
}

/* 標籤響應式樣式 */
.ant-tag {
  border-radius: 4px;
  font-size: 12px;
  padding: 2px 8px;
  margin: 2px;
}

@media (max-width: 768px) {
  .ant-tag {
    font-size: 10px;
    padding: 1px 6px;
    margin: 1px;
  }
}

/* 徽章響應式樣式 */
.ant-badge {
  font-size: 12px;
}

.ant-badge-count {
  font-size: 10px;
  min-width: 16px;
  height: 16px;
  line-height: 16px;
}

@media (max-width: 768px) {
  .ant-badge {
    font-size: 10px;
  }
  
  .ant-badge-count {
    font-size: 8px;
    min-width: 14px;
    height: 14px;
    line-height: 14px;
  }
}

/* 載入動畫響應式樣式 */
.ant-spin {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

@media (max-width: 768px) {
  .ant-spin {
    min-height: 150px;
  }
}

/* 空狀態響應式樣式 */
.ant-empty {
  padding: 40px 0;
}

.ant-empty-description {
  color: #8c8c8c;
  font-size: 14px;
}

@media (max-width: 768px) {
  .ant-empty {
    padding: 20px 0;
  }
  
  .ant-empty-description {
    font-size: 12px;
  }
}

/* 分頁響應式樣式 */
.ant-pagination {
  text-align: center;
  margin-top: 16px;
}

.ant-pagination-item {
  border-radius: 4px;
}

.ant-pagination-item-active {
  background-color: #1890ff;
  border-color: #1890ff;
}

@media (max-width: 768px) {
  .ant-pagination {
    margin-top: 12px;
  }
  
  .ant-pagination-item {
    min-width: 28px;
    height: 28px;
    line-height: 28px;
  }
  
  .ant-pagination-prev,
  .ant-pagination-next {
    min-width: 28px;
    height: 28px;
    line-height: 28px;
  }
}

/* 工具提示響應式樣式 */
.ant-tooltip {
  font-size: 12px;
}

@media (max-width: 768px) {
  .ant-tooltip {
    font-size: 10px;
  }
}

/* 確認框響應式樣式 */
.ant-popconfirm {
  font-size: 12px;
}

@media (max-width: 768px) {
  .ant-popconfirm {
    font-size: 10px;
  }
}

/* 自定義滾動條樣式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 響應式網格系統 */
.ant-row {
  margin-left: -8px;
  margin-right: -8px;
}

.ant-col {
  padding-left: 8px;
  padding-right: 8px;
}

@media (max-width: 768px) {
  .ant-row {
    margin-left: -4px;
    margin-right: -4px;
  }
  
  .ant-col {
    padding-left: 4px;
    padding-right: 4px;
  }
}

/* 動畫效果 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-up {
  animation: slideUp 0.3s ease-in-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 深色模式支援 */
@media (prefers-color-scheme: dark) {
  .responsive-contact-management-page {
    background-color: #141414;
    color: #ffffff;
  }
  
  .responsive-contact-management-page .ant-card {
    background-color: #1f1f1f;
    border-color: #303030;
  }
  
  .responsive-contact-management-page .ant-card-head {
    border-bottom-color: #303030;
  }
  
  .responsive-contact-management-page .ant-table-thead > tr > th {
    background-color: #1f1f1f;
    color: #ffffff;
  }
  
  .responsive-contact-management-page .ant-table-tbody > tr > td {
    background-color: #1f1f1f;
    color: #ffffff;
  }
  
  .responsive-contact-management-page .ant-table-tbody > tr:hover > td {
    background-color: #262626;
  }
}
