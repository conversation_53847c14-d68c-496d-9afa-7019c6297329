# 規格（IMS-Partner-004）

## 使用者故事
- 作為開單人員，我需要在新增/編輯商業夥伴時，能快速新增聯絡人並設定主要、排序，使後續對接更順暢。

## 業務規則
- 夥伴的聯絡人關聯僅允許一位主要（isPrimary=true）
- 若同時多位標記主要，系統自動選 Priority 最小者為主要
- 未填 Priority 預設為 99；主要聯絡人 Priority=0

## 介面/資料結構
- `PartnerContact` 欄位：`contactID`、`role?`、`isPrimary`、`priority`、`notes?` 等
- 與後端 DTO 一致，不使用 `contactRoleID`

## 流程
1. 在 `ContactManagementTab` 操作關聯清單，支援新增/編輯/刪除/排序/指定主要
2. 在 `PartnerFormModal` 提交時，套用 normalize 規則
3. 送往 Partner 聚合 API（沿用現有 CRUD）

## 驗收
- 新增/編輯夥伴均成功提交，且聯絡人清單符合規則
- 快速新增聯絡人後立即可於清單中管理並隨夥伴提交
- 無 Type/Lint 錯誤
