"use client";

import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Table, 
  Button, 
  Space, 
  Modal, 
  Form, 
  Input, 
  Select, 
  message, 
  Popconfirm, 
  Tag, 
  Tooltip, 
  InputNumber,
  Row,
  Col,
  AutoComplete,
  Switch
} from 'antd';

// 響應式組件
import ResponsiveContactManagementCenter from './shared/ResponsiveContactManagementCenter';
import { 
  PlusOutlined, 
  EditOutlined, 
  DeleteOutlined, 
  ContactsOutlined,
  DragOutlined,
  StarOutlined,
  StarFilled,
  SearchOutlined 
} from '@ant-design/icons';
import { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';
import { arrayMove, SortableContext, sortableKeyboardCoordinates, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { PartnerContact } from '@/services/ims/partner';
import { getContactList, addContact, searchContacts as search<PERSON>ontacts<PERSON><PERSON>, Contact } from '@/services/ims/ContactService';
import { logger, SYMBOLS, createContextLogger } from '@/utils/logger';
import { useContactState } from '@/contexts/ContactContext';

// 創建上下文日誌器
const contactManagementLogger = createContextLogger({ module: 'ContactManagementTab' });


interface ContactManagementTabProps {
  partnerID?: string;
  partnerContacts: PartnerContact[];
  onChange: (contacts: PartnerContact[]) => void;
  readonly?: boolean;
}

// 常用聯絡人角色選項
const COMMON_ROLES = ['主辦','協辦','其他'];

// 可排序的表格行元件
const SortableRow = ({ children, ...props }: any) => {
  const { attributes,listeners,setNodeRef,transform,transition, isDragging } = useSortable({ id: props['data-row-key'] });

  const style = {
    ...props.style,
    transform: CSS.Transform.toString(transform),
    transition,
    opacity: isDragging ? 0.5 : 1,
  };

  return (
    <tr {...props} ref={setNodeRef} style={style} {...attributes}>
      {React.Children.map(children, (child) => {
        if (child.key === 'sort') {
          return React.cloneElement(child, {
            children: (
              <div
                {...listeners}
                style={{
                  cursor: 'grab',
                  color: '#999',
                }}
              >
                <DragOutlined />
              </div>
            ),
          });
        }
        return child;
      })}
    </tr>
  );
};

const ContactManagementTab: React.FC<ContactManagementTabProps> = ({
  partnerID,
  partnerContacts = [],
  onChange,
  readonly = false,
}) => {
  // 使用 ContactState (不需要 Provider)
  const { contacts: availableContacts, refreshContacts } = useContactState();
  
  const [localContacts, setLocalContacts] = useState<PartnerContact[]>(partnerContacts);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [editingContact, setEditingContact] = useState<PartnerContact | null>(null);
  const [searchContacts, setSearchContacts] = useState<Contact[]>([]);
  const [form] = Form.useForm();
  
  // 快速新增聯絡人相關狀態
  const [isCreateContactModalVisible, setIsCreateContactModalVisible] = useState(false);
  const [createContactForm] = Form.useForm();
  const [isCreatingContact, setIsCreatingContact] = useState(false);

  // 拖拽感應器
  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  useEffect(() => {
    setLocalContacts(partnerContacts);
  }, [partnerContacts]);

  // 初始化搜尋聯絡人
  useEffect(() => {
    setSearchContacts(availableContacts);
  }, [availableContacts]);

  // 處理拖拽結束
  const handleDragEnd = (event: any) => {
    const { active, over } = event;
    
    if (!over || active.id === over.id) {
      return;
    }

    const oldIndex = localContacts.findIndex(item => 
      `${item.partnerID}-${item.contactID}` === active.id
    );
    const newIndex = localContacts.findIndex(item => 
      `${item.partnerID}-${item.contactID}` === over.id
    );

    if (oldIndex !== -1 && newIndex !== -1) {
      const newContacts = arrayMove(localContacts, oldIndex, newIndex);
      
      // 重新分配 Priority（基於新順序）
      const reorderedContacts = newContacts.map((contact, index) => ({
        ...contact,
        priority: index,
      }));

      setLocalContacts(reorderedContacts);
      onChange(reorderedContacts);
      
      contactManagementLogger.log(SYMBOLS.SUCCESS, '聯絡人順序已更新', { 
        from: oldIndex, 
        to: newIndex 
      });
    }
  };

  // 設為主要聯絡人
  const setPrimaryContact = (targetContactId: string) => {
    const updatedContacts = localContacts.map(contact => ({
      ...contact,
      priority: contact.contactID === targetContactId ? 0 : 
                (contact.priority === 0 ? 99 : contact.priority),
      isPrimary: contact.contactID === targetContactId
    }));

    setLocalContacts(updatedContacts);
    onChange(updatedContacts);
    message.success('主要聯絡人已更新');
    
    contactManagementLogger.log(SYMBOLS.SUCCESS, '主要聯絡人已設定', { 
      contactId: targetContactId 
    });
  };

  // 開啟新增/編輯對話框
  const openModal = (contact?: PartnerContact) => {
    setEditingContact(contact || null);
    setIsModalVisible(true);
    
    if (contact) {
      form.setFieldsValue(contact);
    } else {
      form.resetFields();
      form.setFieldsValue({
        partnerID: partnerID,
        priority: 99,
        role: 'Other',
      });
    }
  };

  // 開啟快速新增聯絡人對話框
  const openCreateContactModal = () => {
    setIsCreateContactModalVisible(true);
    createContactForm.resetFields();
    createContactForm.setFieldsValue({
      status: true,
      contactType: '客戶',
    });
  };

  // 快速新增聯絡人並建立關聯
  const handleCreateContact = async (values: any) => {
    try {
      setIsCreatingContact(true);
      
      contactManagementLogger.log(SYMBOLS.LOADING, '快速新增聯絡人', { 
        name: values.name,
        email: values.email 
      });

      // 新增聯絡人
      const response = await addContact({
        name: values.name,
        position: values.position,
        email: values.email,
        phone: values.phone,
        isActive: values.isActive,
        contactType: values.contactType,
        department: values.department,
        company: values.company,
      });

      // 後援：部分環境回傳 data 為 null，需以 email/姓名查回剛建立的聯絡人
      const resolveContactByLookup = async (): Promise<Contact | null> => {
        try {
          const keyword = values.email?.trim() || values.name?.trim();
          if (!keyword) return null;
          const lookup = await searchContactsApi(keyword);
          if (!lookup.success || !lookup.data || lookup.data.length === 0) return null;
          // 以 email 精準比對，否則以名稱比對；若多筆，取 createTime 最大者
          const candidates = lookup.data.filter((c: Contact) => {
            if (values.email?.trim()) {
              return (c.email || '').toLowerCase() === values.email.trim().toLowerCase();
            }
            return (c.name || '').trim() === values.name?.trim();
          });
          const list: Contact[] = candidates.length > 0 ? candidates : (lookup.data as Contact[]);
          const sorted = [...list].sort((a: Contact, b: Contact) => (b.createTime ?? 0) - (a.createTime ?? 0));
          return sorted[0] || null;
        } catch {
          return null;
        }
      };

      if (response.success) {
        const newContact = response.data?.contactID ? response.data : await resolveContactByLookup();
        if (!newContact || !newContact.contactID) {
          throw new Error('無法取得新聯絡人的識別碼');
        }
        
        // 重新載入聯絡人清單
        await refreshContacts();
        
        // 自動建立關聯
        const newPartnerContact: PartnerContact = {
          partnerID: partnerID || '',
          contactID: newContact.contactID,
          role: values.role || 'Other',
          isPrimary: false,
          priority: values.priority || 99,
          notes: values.notes || '',
          expertise: values.expertise || '',
          businessScope: values.businessScope || '',
          workingHours: '',
          languages: '',
          emergencyContact: '',
          emergencyPhone: '',
          // ModelBaseEntityDTO 屬性
          createTime: Date.now(),
          createUserId: null,
          updateTime: Date.now(),
          updateUserId: null,
          deleteTime: null,
          deleteUserId: null,
          isDeleted: false,
        };

        // 正規化：去重、單一主要、Priority 合理化
        const normalizePartnerContacts = (contacts: PartnerContact[]): PartnerContact[] => {
          if (!contacts || contacts.length === 0) return [];
          const uniqueMap = new Map<string, PartnerContact>();
          for (const c of contacts) {
            if (!c?.contactID) continue;
            uniqueMap.set(c.contactID, c);
          }
          let list = Array.from(uniqueMap.values());
          const primaries = list.filter(c => c.isPrimary);
          if (primaries.length > 1) {
            const primary = primaries.sort((a, b) => (a.priority ?? 99) - (b.priority ?? 99))[0];
            list = list.map(c => ({ ...c, isPrimary: c.contactID === primary.contactID }));
          }
          if (list.length > 0 && !list.some(c => c.isPrimary)) {
            const first = [...list].sort((a, b) => (a.priority ?? 99) - (b.priority ?? 99))[0];
            list = list.map(c => ({ ...c, isPrimary: c.contactID === first.contactID }));
          }
          list = list.map(c => ({
            ...c,
            priority: c.isPrimary ? 0 : (typeof c.priority === 'number' ? c.priority : 99),
            partnerID: partnerID || c.partnerID || ('' as any),
          }));
          return list;
        };

        const normalized = normalizePartnerContacts([...localContacts, newPartnerContact]);
        setLocalContacts(normalized);
        onChange(normalized);

        contactManagementLogger.log(SYMBOLS.SUCCESS, '聯絡人新增並關聯成功', { 
          contactId: newContact.contactID,
          contactName: newContact.name
        });

        message.success('聯絡人新增並關聯成功');
        setIsCreateContactModalVisible(false);
        createContactForm.resetFields();
      } else {
        throw new Error(response.message || '新增聯絡人失敗');
      }
    } catch (error) {
      contactManagementLogger.log(SYMBOLS.ERROR, '快速新增聯絡人失敗', error);
      message.error('新增聯絡人失敗，請重試');
    } finally {
      setIsCreatingContact(false);
    }
  };

  // 搜尋聯絡人
  const handleContactSearch = (value: string) => {
    const filtered = availableContacts.filter(contact =>
      contact.name.toLowerCase().includes(value.toLowerCase()) ||
      (contact.company && contact.company.toLowerCase().includes(value.toLowerCase())) ||
      (contact.email && contact.email.toLowerCase().includes(value.toLowerCase()))
    );
    setSearchContacts(filtered);
  };

  // 儲存聯絡人關聯
  const handleSave = async (values: any) => {
    try {
      // 檢查是否已存在相同聯絡人關聯
      const existingContact = localContacts.find(pc => 
        pc.contactID === values.contactID && 
        (!editingContact || pc.contactID !== editingContact.contactID)
      );

      if (existingContact) {
        message.error('此聯絡人已經建立關聯');
        return;
      }

      const contactData: PartnerContact = {
        ...values,
        partnerID: partnerID || values.partnerID,
        createTime: editingContact?.createTime || Date.now(),
        createUserId: editingContact?.createUserId || null,
        updateTime: Date.now(),
        updateUserId: null,
        deleteTime: null,
        deleteUserId: null,
        isDeleted: false,
      };

      let updatedContacts: PartnerContact[];
      
      if (editingContact) {
        // 編輯現有關聯
        updatedContacts = localContacts.map(pc => 
          pc.contactID === editingContact.contactID ? contactData : pc
        );
      } else {
        // 新增關聯
        updatedContacts = [...localContacts, contactData];
      }

      // 正規化：去重、單一主要、Priority 合理化
      const normalizePartnerContacts = (contacts: PartnerContact[]): PartnerContact[] => {
        if (!contacts || contacts.length === 0) return [];
        const uniqueMap = new Map<string, PartnerContact>();
        for (const c of contacts) {
          if (!c?.contactID) continue;
          uniqueMap.set(c.contactID, c);
        }
        let list = Array.from(uniqueMap.values());
        const primaries = list.filter(c => c.isPrimary);
        if (primaries.length > 1) {
          const primary = primaries.sort((a, b) => (a.priority ?? 99) - (b.priority ?? 99))[0];
          list = list.map(c => ({ ...c, isPrimary: c.contactID === primary.contactID }));
        }
        if (list.length > 0 && !list.some(c => c.isPrimary)) {
          const first = [...list].sort((a, b) => (a.priority ?? 99) - (b.priority ?? 99))[0];
          list = list.map(c => ({ ...c, isPrimary: c.contactID === first.contactID }));
        }
        list = list.map(c => ({
          ...c,
          priority: c.isPrimary ? 0 : (typeof c.priority === 'number' ? c.priority : 99),
          partnerID: partnerID || c.partnerID || ('' as any),
        }));
        return list;
      };

      const normalized = normalizePartnerContacts(updatedContacts);
      setLocalContacts(normalized);
      onChange(normalized);
      setIsModalVisible(false);
      message.success(editingContact ? '聯絡人關聯已更新' : '聯絡人關聯已新增');
      
      contactManagementLogger.log(SYMBOLS.SUCCESS, editingContact ? '聯絡人關聯已更新' : '聯絡人關聯已新增', { 
        contactId: contactData.contactID 
      });
    } catch (error) {
      contactManagementLogger.log(SYMBOLS.ERROR, '儲存聯絡人關聯失敗', error);
      message.error('儲存失敗，請重試');
    }
  };

  // 刪除聯絡人關聯
  const handleDelete = (contactId: string) => {
    const updatedContacts = localContacts.filter(pc => pc.contactID !== contactId);
    // 刪除後同樣進行正規化，確保主要與 Priority 正確
    const normalizePartnerContacts = (contacts: PartnerContact[]): PartnerContact[] => {
      if (!contacts || contacts.length === 0) return [];
      let list = [...contacts];
      const primaries = list.filter(c => c.isPrimary);
      if (primaries.length > 1) {
        const primary = primaries.sort((a, b) => (a.priority ?? 99) - (b.priority ?? 99))[0];
        list = list.map(c => ({ ...c, isPrimary: c.contactID === primary.contactID }));
      }
      if (list.length > 0 && !list.some(c => c.isPrimary)) {
        const first = [...list].sort((a, b) => (a.priority ?? 99) - (b.priority ?? 99))[0];
        list = list.map(c => ({ ...c, isPrimary: c.contactID === first.contactID }));
      }
      list = list.map(c => ({
        ...c,
        priority: c.isPrimary ? 0 : (typeof c.priority === 'number' ? c.priority : 99),
        partnerID: partnerID || c.partnerID || ('' as any),
      }));
      return list;
    };

    const normalized = normalizePartnerContacts(updatedContacts);
    setLocalContacts(normalized);
    onChange(normalized);
    message.success('聯絡人關聯已刪除');
    
    contactManagementLogger.log(SYMBOLS.SUCCESS, '聯絡人關聯已刪除', { contactId });
  };

  // 獲取聯絡人詳細資訊
  const getContactInfo = (contactId: string) => {
    return availableContacts.find(c => c.contactID === contactId);
  };

  // 表格欄位定義
  const columns = [
    {
      key: 'sort',
      width: 50,
      render: () => null, // 由 SortableRow 處理
    },
    {
      title: '優先序',
      dataIndex: 'priority',
      key: 'priority',
      width: 80,
      render: (priority: number, record: PartnerContact) => (
        <Space>
          {priority === 0 ? (
            <Tag color="gold" icon={<StarFilled />}>主要</Tag>
          ) : (
            <Tag>{priority}</Tag>
          )}
        </Space>
      ),
    },
    {
      title: '聯絡人資訊',
      key: 'contactInfo',
      render: (record: PartnerContact) => {
        const contactInfo = getContactInfo(record.contactID);
        return (
          <div>
            <div style={{ fontWeight: 'bold' }}>
              {contactInfo?.name || '未知聯絡人'}
            </div>
            {contactInfo?.position && (
              <div style={{ color: '#666', fontSize: '12px' }}>
                {contactInfo.position}
              </div>
            )}
            {contactInfo?.company && (
              <div style={{ color: '#999', fontSize: '11px' }}>
                {contactInfo.company}
              </div>
            )}
          </div>
        );
      },
    },
    {
      title: '角色',
      dataIndex: 'role',
      key: 'role',
      width: 120,
      render: (role: string) => (
        <Tag color="blue">{role}</Tag>
      ),
    },
    {
      title: '備註',
      dataIndex: 'notes',
      key: 'notes',
      ellipsis: true,
      render: (notes: string) => (
        <Tooltip title={notes}>
          <span>{notes}</span>
        </Tooltip>
      ),
    },
    {
      title: '操作',
      key: 'actions',
      width: 150,
      render: (record: PartnerContact) => (
        <Space>
          {record.priority !== 0 && (
            <Tooltip title="設為主要聯絡人">
              <Button 
                type="text" 
                size="small" 
                icon={<StarOutlined />}
                onClick={() => setPrimaryContact(record.contactID)}
                disabled={readonly}
              />
            </Tooltip>
          )}
          <Button 
            type="text" 
            size="small" 
            icon={<EditOutlined />}
            onClick={() => openModal(record)}
            disabled={readonly}
          />
          <Popconfirm
            title="確定要刪除這個聯絡人關聯嗎？"
            description="此操作將永久刪除關聯，無法復原。"
            onConfirm={() => handleDelete(record.contactID)}
            disabled={readonly}
          >
            <Button 
              type="text" 
              size="small" 
              danger 
              icon={<DeleteOutlined />}
              disabled={readonly}
            />
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <Card
      title={
        <Space>
          <ContactsOutlined />
          <span>聯絡人管理</span>
          <Tag color="blue">{localContacts.length} 個關聯</Tag>
        </Space>
      }
      extra={
        !readonly && (
          <Space wrap>
            <Button
              type="default"
              icon={<PlusOutlined />}
              onClick={() => openModal()}
              size="small"
            >
              新增關聯
            </Button>
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => openCreateContactModal()}
              size="small"
            >
              快速新增
            </Button>
          </Space>
        )
      }
      size="small"
    >
      <DndContext 
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragEnd={handleDragEnd}
      >
        <SortableContext 
          items={localContacts.map(pc => `${pc.partnerID}-${pc.contactID}`)}
          strategy={verticalListSortingStrategy}
        >
          <Table
            components={{
              body: {
                row: SortableRow,
              },
            }}
            rowKey={(record) => `${record.partnerID}-${record.contactID}`}
            columns={columns}
            dataSource={localContacts.sort((a, b) => a.priority - b.priority)}
            pagination={false}
            size="small"
            locale={{ emptyText: '尚未建立任何聯絡人關聯' }}
          />
        </SortableContext>
      </DndContext>

      {/* 新增/編輯聯絡人關聯對話框 */}
      <Modal
        title={editingContact ? '編輯聯絡人關聯' : '新增聯絡人關聯'}
        open={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        onOk={() => form.submit()}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSave}
        >
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                label="選擇聯絡人"
                name="contactID"
                rules={[{ required: true, message: '請選擇聯絡人' }]}
              >
                <Select
                  placeholder="搜尋並選擇聯絡人"
                  showSearch
                  filterOption={false}
                  onSearch={handleContactSearch}
                  notFoundContent="無符合的聯絡人"
                >
                  {searchContacts.map(contact => (
                    <Select.Option key={contact.contactID} value={contact.contactID}>
                      <div>
                        <div style={{ fontWeight: 'bold' }}>{contact.name}</div>
                        <div style={{ fontSize: '12px', color: '#666' }}>
                          {contact.position && `${contact.position} | `}
                          {contact.company && `${contact.company} | `}
                          {contact.email}
                        </div>
                      </div>
                    </Select.Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="角色"
                name="role"
                rules={[{ required: true, message: '請輸入角色' }]}
              >
                <AutoComplete
                  options={COMMON_ROLES.map(role => ({ value: role }))}
                  placeholder="請輸入或選擇角色"
                  filterOption={(inputValue, option) =>
                    option!.value.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1
                  }
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="優先序"
                name="priority"
                tooltip="數字越小優先級越高，0 代表主要聯絡人"
              >
                <InputNumber
                  min={0}
                  max={999}
                  style={{ width: '100%' }}
                  placeholder="預設 99"
                  defaultValue={99}
                />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                label="備註"
                name="notes"
              >
                <Input.TextArea 
                  rows={2}
                  placeholder="請輸入備註資訊"
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="專業領域"
                name="expertise"
              >
                <Input placeholder="例如：技術支援、採購管理" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="業務範圍"
                name="businessScope"
              >
                <Input placeholder="例如：北區業務、海外市場" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>

      {/* 快速新增聯絡人對話框 */}
      <Modal
        title="快速新增聯絡人"
        open={isCreateContactModalVisible}
        onCancel={() => setIsCreateContactModalVisible(false)}
        onOk={() => createContactForm.submit()}
        confirmLoading={isCreatingContact}
        width={600}
      >
        <Form
          form={createContactForm}
          layout="vertical"
          onFinish={handleCreateContact}
        >
          {/* 基本聯絡人資訊 */}
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                label="姓名"
                name="name"
                rules={[{ required: true, message: '請輸入姓名' }]}
              >
                <Input placeholder="請輸入聯絡人姓名" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="職位"
                name="position"
              >
                <Input placeholder="請輸入職位" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="電子信箱"
                name="email"
                rules={[{ type: 'email', message: '請輸入有效的電子信箱' }]}
              >
                <Input placeholder="請輸入電子信箱" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="電話"
                name="phone"
              >
                <Input placeholder="請輸入電話號碼" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="公司"
                name="company"
              >
                <Input placeholder="請輸入公司名稱" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="聯絡人類型"
                name="contactType"
                initialValue="external"
              >
                <Select>
                  <Select.Option value="internal">內部員工</Select.Option>
                  <Select.Option value="external">外部聯絡人</Select.Option>
                  <Select.Option value="customer">客戶</Select.Option>
                  <Select.Option value="supplier">供應商</Select.Option>
                  <Select.Option value="partner">合作夥伴</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="狀態"
                name="isActive"
                valuePropName="checked"
                initialValue={true}
              >
                <Switch checkedChildren="啟用" unCheckedChildren="停用" />
              </Form.Item>
            </Col>
          </Row>
          
          {/* 額外的夥伴聯絡人欄位 */}
          <Row gutter={16}>
            <Col span={24}>
              <Form.Item
                label="角色"
                name="role"
                rules={[{ required: true, message: '請輸入角色' }]}
              >
                <AutoComplete
                  options={COMMON_ROLES.map(role => ({ value: role }))}
                  placeholder="請輸入或選擇角色"
                  filterOption={(inputValue, option) =>
                    option!.value.toUpperCase().indexOf(inputValue.toUpperCase()) !== -1
                  }
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="優先序"
                name="priority"
                tooltip="數字越小優先級越高，0 代表主要聯絡人"
              >
                <InputNumber
                  min={0}
                  max={999}
                  style={{ width: '100%' }}
                  placeholder="預設 99"
                  defaultValue={99}
                />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                label="專業領域"
                name="expertise"
              >
                <Input placeholder="例如：技術支援、採購管理" />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                label="備註"
                name="notes"
              >
                <Input.TextArea 
                  rows={2}
                  placeholder="請輸入備註資訊"
                />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                label="業務範圍"
                name="businessScope"
              >
                <Input placeholder="例如：北區業務、海外市場" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </Card>
  );
};

export default ContactManagementTab;
