# IMS-Contact-001 API 測試文件

## 📋 API 端點清單

### 現有 API 端點
- `GET /api/ims/Contact` - 取得聯絡人列表
- `POST /api/ims/Contact` - 新增聯絡人
- `PUT /api/ims/Contact/{id}` - 更新聯絡人
- `DELETE /api/ims/Contact/{id}` - 刪除聯絡人
- `GET /api/ims/Contact/{id}` - 取得單一聯絡人

### 新增 API 端點
- `GET /api/ims/Contact/stats` - 取得聯絡人統計資訊
- `GET /api/ims/Contact/search` - 搜尋聯絡人
- `GET /api/ims/Contact/filter` - 篩選聯絡人

## 🧪 API 測試案例

### 1. 聯絡人統計 API 測試

#### 測試案例 1: 取得聯絡人統計資訊
```http
GET /api/ims/Contact/stats
Authorization: Bearer {token}
Content-Type: application/json
```

**預期回應**:
```json
{
  "success": true,
  "data": {
    "totalContacts": 150,
    "activeContacts": 120,
    "inactiveContacts": 30,
    "contactsByType": {
      "客戶": 80,
      "供應商": 50,
      "其他": 20
    }
  },
  "message": "統計資訊取得成功"
}
```

### 2. 聯絡人搜尋 API 測試

#### 測試案例 1: 基本搜尋
```http
GET /api/ims/Contact/search?keyword=張三
Authorization: Bearer {token}
Content-Type: application/json
```

**預期回應**:
```json
{
  "success": true,
  "data": {
    "contacts": [
      {
        "contactID": "123e4567-e89b-12d3-a456-426614174000",
        "name": "張三",
        "position": "業務經理",
        "email": "<EMAIL>",
        "phone": "0912345678",
        "company": "ABC公司",
        "contactType": "客戶",
        "status": true,
        "createTime": "2024-12-19T10:00:00Z",
        "updateTime": "2024-12-19T10:00:00Z"
      }
    ],
    "total": 1,
    "page": 1,
    "pageSize": 20
  },
  "message": "搜尋成功"
}
```

#### 測試案例 2: 進階搜尋
```http
GET /api/ims/Contact/search?keyword=經理&contactType=客戶&status=true&page=1&pageSize=10
Authorization: Bearer {token}
Content-Type: application/json
```

### 3. 聯絡人篩選 API 測試

#### 測試案例 1: 按類型篩選
```http
GET /api/ims/Contact/filter?contactType=客戶&status=true
Authorization: Bearer {token}
Content-Type: application/json
```

**預期回應**:
```json
{
  "success": true,
  "data": {
    "contacts": [
      {
        "contactID": "123e4567-e89b-12d3-a456-426614174000",
        "name": "張三",
        "position": "業務經理",
        "email": "<EMAIL>",
        "phone": "0912345678",
        "company": "ABC公司",
        "contactType": "客戶",
        "status": true
      }
    ],
    "total": 80,
    "page": 1,
    "pageSize": 20
  },
  "message": "篩選成功"
}
```

## 🔧 API 實作規格

### 1. 聯絡人統計 API

#### 端點
```
GET /api/ims/Contact/stats
```

#### 請求參數
無

#### 回應格式
```typescript
interface ContactStatsResponse {
  success: boolean;
  data: {
    totalContacts: number;
    activeContacts: number;
    inactiveContacts: number;
    contactsByType: Record<string, number>;
  };
  message?: string;
}
```

#### 實作邏輯
1. 計算總聯絡人數
2. 計算啟用聯絡人數
3. 計算停用聯絡人數
4. 按聯絡人類型分組統計
5. 回傳統計結果

### 2. 聯絡人搜尋 API

#### 端點
```
GET /api/ims/Contact/search
```

#### 請求參數
```typescript
interface ContactSearchRequest {
  keyword: string;           // 搜尋關鍵字
  contactType?: string;      // 聯絡人類型
  status?: boolean;          // 狀態
  page?: number;            // 頁碼
  pageSize?: number;        // 每頁筆數
}
```

#### 回應格式
```typescript
interface ContactSearchResponse {
  success: boolean;
  data: {
    contacts: Contact[];
    total: number;
    page: number;
    pageSize: number;
  };
  message?: string;
}
```

#### 實作邏輯
1. 驗證請求參數
2. 建立搜尋條件
3. 執行資料庫查詢
4. 分頁處理
5. 回傳搜尋結果

### 3. 聯絡人篩選 API

#### 端點
```
GET /api/ims/Contact/filter
```

#### 請求參數
```typescript
interface ContactFilterRequest {
  contactType?: string;      // 聯絡人類型
  status?: boolean;          // 狀態
  company?: string;          // 公司名稱
  department?: string;       // 部門
  page?: number;            // 頁碼
  pageSize?: number;        // 每頁筆數
}
```

#### 回應格式
```typescript
interface ContactFilterResponse {
  success: boolean;
  data: {
    contacts: Contact[];
    total: number;
    page: number;
    pageSize: number;
  };
  message?: string;
}
```

## 🧪 測試環境設定

### 1. 測試資料準備
```sql
-- 插入測試聯絡人資料
INSERT INTO Contact (ContactID, Name, Position, Email, Phone, Company, ContactType, Status, CreateTime, IsDeleted)
VALUES 
('123e4567-e89b-12d3-a456-426614174000', '張三', '業務經理', '<EMAIL>', '0912345678', 'ABC公司', '客戶', 1, GETDATE(), 0),
('123e4567-e89b-12d3-a456-426614174001', '李四', '採購專員', '<EMAIL>', '0912345679', 'XYZ公司', '供應商', 1, GETDATE(), 0),
('123e4567-e89b-12d3-a456-426614174002', '王五', '客服主管', '<EMAIL>', '0912345680', 'DEF公司', '客戶', 0, GETDATE(), 0);
```

### 2. 測試工具
- **Postman**: API 測試
- **Swagger**: API 文件
- **Unit Tests**: 單元測試
- **Integration Tests**: 整合測試

### 3. 測試環境
- **開發環境**: localhost:5000
- **測試環境**: test.fasterp.com
- **正式環境**: api.fasterp.com

## 📋 測試檢查清單

### 功能測試
- [ ] 聯絡人統計 API 正常運作
- [ ] 聯絡人搜尋 API 正常運作
- [ ] 聯絡人篩選 API 正常運作
- [ ] 分頁功能正常運作
- [ ] 錯誤處理正常運作

### 效能測試
- [ ] API 回應時間 < 1秒
- [ ] 支援大量資料查詢
- [ ] 記憶體使用合理
- [ ] 資料庫查詢優化

### 安全性測試
- [ ] 權限驗證正常
- [ ] SQL 注入防護
- [ ] XSS 防護
- [ ] CSRF 防護

### 相容性測試
- [ ] 支援不同瀏覽器
- [ ] 支援不同裝置
- [ ] 支援不同網路環境

## 🚀 部署檢查清單

### 開發環境
- [ ] API 端點正常運作
- [ ] 資料庫連線正常
- [ ] 日誌記錄正常
- [ ] 錯誤處理正常

### 測試環境
- [ ] 完整功能測試通過
- [ ] 效能測試通過
- [ ] 安全性測試通過
- [ ] 相容性測試通過

### 正式環境
- [ ] 生產環境部署
- [ ] 監控設定
- [ ] 備份策略
- [ ] 回滾計劃

---

**注意**: 所有 API 測試都應該在開發完成後進行，確保功能正常運作。
