# IMS-Contact-003：聯絡人表單驗證規則與欄位同步

## 任務基本資訊
- 任務代碼：IMS-Contact-003
- 任務標題：聯絡人表單驗證規則與欄位同步
- 所屬模組：IMS（庫存管理系統）
- 需求提出：用戶反饋（基於實際使用發現的問題）
- 相關任務：IMS-Contact-002
- 建立日期：2024-12-19
- 狀態：🚧 待開始

## 問題描述

### 發現的問題
經過實際使用測試，發現聯絡人管理存在以下嚴重問題：

1. **表單欄位不一致**：
   - 快速新增聯絡人（quick 模式）：只有姓名、電子郵件、聯絡人類型、狀態 4 個欄位
   - 完整新增聯絡人（full 模式）：包含所有 8 個欄位（姓名、職位、電子郵件、電話、部門、公司、聯絡人類型、狀態）
   - 用戶期望快速新增後的資料能完整帶入編輯表單，但缺少的欄位會顯示為空

2. **必填欄位設定不當**：
   - 前端設定「優先序」為必填，但後端模型中 `Priority` 有預設值 99，不應為必填
   - 前端設定「角色」為必填，但後端模型中 `Role` 為可空字串，不應為必填
   - 前端驗證規則與後端模型驗證屬性不一致

3. **前後端驗證不同步**：
   - 後端 `Contact` 模型：只有 `Name` 標記為 `[Required]`
   - 前端驗證：姓名、角色、優先序都設為必填
   - 造成前端驗證通過但後端可能拒絕，或前端過度限制用戶輸入

4. **資料流轉問題**：
   - 快速新增聯絡人後，資料帶入商業夥伴聯絡人管理時，優先序等欄位為空或預設值
   - 用戶需要重新填寫已經在快速新增時應該設定的資料

### 影響範圍
- ContactFormFields 組件（快速與完整模式）
- ContactManagementTab 組件（快速新增聯絡人功能）
- 前端驗證規則與後端模型驗證的一致性
- 用戶體驗：表單填寫的連續性和一致性

## 解決目標

### 主要目標
1. **統一表單欄位**：快速新增與完整新增應包含相同的核心欄位
2. **同步驗證規則**：前端驗證規則必須與後端模型驗證屬性完全一致
3. **優化必填邏輯**：根據業務需求和後端模型重新設計必填欄位
4. **改善資料流轉**：確保快速新增的資料能完整帶入後續編輯流程

### 具體改進
1. **欄位統一**：
   - 快速模式增加：職位、電話、部門、公司欄位
   - 保持快速模式的簡潔性：使用更好的佈局和分組

2. **驗證規則同步**：
   - 前端只將後端標記為 `[Required]` 的欄位設為必填
   - 移除不合理的必填限制（如優先序、角色）
   - 統一錯誤訊息格式和內容

3. **預設值處理**：
   - 優先序預設為 99（與後端一致）
   - 角色預設為空，允許用戶選填
   - 聯絡人類型預設為「客戶」

## 技術方案

### 階段一：驗證規則同步
1. **後端驗證分析**：
   - 分析所有聯絡人相關模型的驗證屬性
   - 整理出標準的必填欄位清單
   - 確認預設值和業務規則

2. **前端驗證更新**：
   - 更新 `contactValidation.ts` 中的驗證規則
   - 移除不合理的必填限制
   - 統一錯誤訊息

### 階段二：表單欄位優化
1. **快速模式改進**：
   - 增加職位、電話欄位（常用但非必填）
   - 優化佈局：使用分組或摺疊設計
   - 保持操作的快速性

2. **完整模式優化**：
   - 確保所有欄位都有適當的預設值
   - 優化欄位順序和分組

### 階段三：資料流轉優化
1. **預設值處理**：
   - 確保所有表單都使用統一的預設值
   - 優化資料帶入邏輯

2. **用戶體驗改進**：
   - 提供更好的欄位說明和提示
   - 優化必填欄位的視覺標示

## 驗收標準

### 功能驗收
- [ ] 快速新增聯絡人包含所有必要欄位（姓名、職位、電子郵件、電話、聯絡人類型、狀態）
- [ ] 前端驗證規則與後端模型驗證屬性完全一致
- [ ] 只有「姓名」為必填欄位，其他欄位均為選填
- [ ] 優先序不再為必填，預設值為 99
- [ ] 角色不再為必填，允許用戶選填
- [ ] 快速新增的資料能完整帶入後續編輯流程

### 技術驗收
- [ ] 所有表單驗證通過前後端一致性測試
- [ ] 移除所有不合理的必填限制
- [ ] 統一所有預設值設定
- [ ] 所有編譯和 lint 檢查通過

### 用戶體驗驗收
- [ ] 快速新增聯絡人操作仍然保持簡潔快速
- [ ] 表單填寫流程順暢，無不必要的阻塞
- [ ] 錯誤訊息清晰準確
- [ ] 資料在不同表單間流轉無遺失

## 風險評估

### 中等風險
- **影響現有功能**：修改驗證規則可能影響現有的表單提交
- **用戶習慣改變**：移除必填限制可能需要用戶適應
- **緩解措施**：分階段部署，充分測試，提供用戶說明

### 低風險
- **技術實現複雜度**：主要是配置和規則調整，技術風險較低
- **回歸風險**：有完整的測試覆蓋，回歸風險可控

## 預期效益

1. **用戶體驗提升**：表單填寫更順暢，減少不必要的必填限制
2. **資料一致性**：快速新增與完整新增的資料結構一致
3. **開發效率**：前後端驗證規則統一，減少維護成本
4. **系統穩定性**：消除前後端驗證不一致導致的問題

## 相關資源
- [IMS-Contact-002](../IMS-Contact-002/)：聯絡人管理統一架構重構
- [DEVELOPMENT_GUIDELINES.md](../../DEVELOPMENT_GUIDELINES.md)
- [架構一致性檢查清單](../_TEMPLATE/examples/architecture_consistency_checklist.md)
