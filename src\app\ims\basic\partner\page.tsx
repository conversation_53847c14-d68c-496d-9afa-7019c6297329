"use client";

import React, {
  useEffect,
  useState,
  useCallback,
  useMemo,
  useRef,
} from "react";
import {
  Card,
  Spin,
  Button,
  Space,
  Modal,
  message,
  Tag,
  Tooltip,
  Popconfirm,
  Typography,
  Statistic,
  Row,
  Col,
  Tabs,
  Badge,
} from "antd";
import {
  EditOutlined,
  DeleteOutlined,
  PlusOutlined,
  ContactsOutlined,
  UserOutlined,
  TeamOutlined,
  SyncOutlined,
  SettingOutlined,
  UnorderedListOutlined,
  TagsOutlined,
  ShopOutlined,
  CheckCircleOutlined,
  StopOutlined,
  CloudServerOutlined,
} from "@ant-design/icons";

// Services and Types
import {
  Partner,
  CustomerCategory,
  SupplierCategory,
} from "@/services/ims/partner";
import {
  getPartnerList,
  addPartner,
  editPartner,
  deletePartner,
  preparePartnerAddData,
  preparePartnerEditData,
} from "@/services/ims/PartnerService";
import {
  getCustomerCategoryList,
  buildCustomerCategoryTree,
} from "@/services/ims/CustomerCategoryService";
import {
  getSupplierCategoryList,
  buildSupplierCategoryTree,
} from "@/services/ims/SupplierCategoryService";
import { addContact, updateContact, Contact } from "@/services/ims/ContactService";

// Components
import PartnerFormModal from "@/app/ims/components/PartnerFormModal";
import CustomerCategoryAdapter from "@/app/ims/components/shared/CustomerCategoryAdapter";
import SupplierCategoryAdapter from "@/app/ims/components/shared/SupplierCategoryAdapter";
import FilterSearchContainer from "@/app/ims/components/shared/FilterSearchContainer";
import ResponsiveStyles from "@/app/ims/components/shared/ResponsiveStyles";
import ResponsiveTable from "@/app/ims/components/shared/ResponsiveTable";
import { 
  ResponsiveContactManagementCenter,
  ResponsiveContactFormModal 
} from "@/app/ims/components/contact";
import { FilterOption } from "@/app/ims/types/filter";

// Data Validation Utils
import {
  processApiResponse,
  safeString,
  safeBoolean,
} from "@/utils/dataValidation";

// Development Logging
import { logger, SYMBOLS, createContextLogger } from "@/utils/logger";

// 創建上下文日誌器
const partnerPageLogger = createContextLogger({ module: "PartnerPage" });

const { Title } = Typography;

// 整合資料介面
interface IntegratedData {
  partners: Partner[];
  customerCategories: CustomerCategory[];
  supplierCategories: SupplierCategory[];
}

// 統計資料介面
interface StatsData {
  totalPartners: number;
  customerCount: number;
  supplierCount: number;
}

const PartnerPage = () => {
  const [data, setData] = useState<IntegratedData>({
    partners: [],
    customerCategories: [],
    supplierCategories: [],
  });
  const [stats, setStats] = useState<StatsData>({
    totalPartners: 0,
    customerCount: 0,
    supplierCount: 0,
  });
  const [filteredPartners, setFilteredPartners] = useState<Partner[]>([]);
  const [loading, setLoading] = useState(false);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedPartner, setSelectedPartner] = useState<Partner | null>(null);
  const [isMobile, setIsMobile] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [isCustomerCategoryModalVisible, setIsCustomerCategoryModalVisible] =
    useState(false);
  const [isSupplierCategoryModalVisible, setIsSupplierCategoryModalVisible] =
    useState(false);
  const [activeTab, setActiveTab] = useState<string>("all");

  // 聯絡人管理狀態
  const [isContactManagementVisible, setIsContactManagementVisible] = useState(false);

  // 標籤篩選狀態
  const [tagFilters, setTagFilters] = useState<{
    type?: "individual" | "enterprise";
    role?: "customer" | "supplier";
    category?: string;
  }>({});

  // FilterSearchContainer 控制 ref - 用於標籤頁切換時清除篩選
  const filterSearchRef = useRef<{ clearAll: () => void } | null>(null);

  // 轉換客戶分類為 TreeSelect 資料格式
  const convertCustomerCategoriesToTreeData = (
    categories: CustomerCategory[]
  ): any[] => {
    return categories.map((category) => ({
      title: category.name,
      value: category.customerCategoryID,
      key: category.customerCategoryID,
      children:
        category.children && category.children.length > 0
          ? convertCustomerCategoriesToTreeData(category.children)
          : undefined,
    }));
  };

  // 轉換供應商分類為 TreeSelect 資料格式
  const convertSupplierCategoriesToTreeData = (
    categories: SupplierCategory[]
  ): any[] => {
    return categories.map((category) => ({
      title: category.name,
      value: category.supplierCategoryID,
      key: category.supplierCategoryID,
      children:
        category.children && category.children.length > 0
          ? convertSupplierCategoriesToTreeData(category.children)
          : undefined,
    }));
  };

  // 移動端檢測
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= 768);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);

    return () => {
      window.removeEventListener("resize", checkMobile);
    };
  }, []);

  // Calculate stats
  const calculateStats = useCallback((partnersData: Partner[]) => {
    const newStats: StatsData = {
      totalPartners: partnersData.length,
      customerCount: partnersData.filter((p) => p.customerDetail != null)
        .length,
      supplierCount: partnersData.filter((p) => p.supplierDetail != null)
        .length,
    };
    setStats(newStats);
  }, []);

  // 清除所有標籤篩選
  const clearTagFilters = useCallback(() => {
    setTagFilters({});
  }, []);

  // 根據標籤頁篩選夥伴
  const filterPartnersByTab = useCallback(
    (partners: Partner[], tab: string): Partner[] => {
      if (!Array.isArray(partners)) return [];

      switch (tab) {
        case "all":
          return partners;
        case "customer":
          return partners.filter((partner) => partner.customerDetail);
        case "supplier":
          return partners.filter((partner) => partner.supplierDetail);
        case "both":
          return partners.filter(
            (partner) => partner.customerDetail && partner.supplierDetail
          );
        default:
          return partners;
      }
    },
    []
  );

  // 處理標籤頁切換 - 整合篩選清除功能並自動應用對應篩選條件
  const handleTabChange = useCallback(
    (key: string) => {
      setActiveTab(key);
      setCurrentPage(1); // 重置分頁

      // 清除 FilterSearchContainer 的篩選條件
      if (filterSearchRef.current) {
        filterSearchRef.current.clearAll();
      }

      // 清除標籤篩選
      clearTagFilters();

      // 重新應用標籤頁篩選
      const tabFiltered = filterPartnersByTab(data.partners, key);
      setFilteredPartners(tabFiltered);

      partnerPageLogger.log(SYMBOLS.INFO, "切換標籤頁並清除篩選:", {
        newTab: key,
        totalPartners: data.partners.length,
        filteredCount: tabFiltered.length,
        filtersCleared: true,
        autoFilterApplied: key !== "all" && key !== "both" ? key : "none",
      });
    },
    [data.partners, filterPartnersByTab, clearTagFilters]
  );

  // 載入所有資料並重置篩選條件
  const loadAllData = useCallback(async () => {
    setLoading(true);
    try {
      partnerPageLogger.log(
        SYMBOLS.LOADING,
        "開始載入 Partner 模組資料並重置篩選..."
      );

      // 清除所有篩選條件
      if (filterSearchRef.current) {
        filterSearchRef.current.clearAll();
      }

      // 清除標籤篩選
      clearTagFilters();

      // 重置標籤頁到全部
      setActiveTab("all");
      setCurrentPage(1);

      // 並行載入所有資料
      const [partnersRes, customerCategoriesRes, supplierCategoriesRes] =
        await Promise.all([
          getPartnerList(),
          getCustomerCategoryList(),
          getSupplierCategoryList(),
        ]);

      // 處理 API 回應
      const partnersResult = processApiResponse<Partner>(
        partnersRes,
        "商業夥伴"
      );
      const customerCategoriesResult = processApiResponse<CustomerCategory>(
        customerCategoriesRes,
        "客戶分類"
      );
      const supplierCategoriesResult = processApiResponse<SupplierCategory>(
        supplierCategoriesRes,
        "供應商分類"
      );

      // 更新狀態
      const newData: IntegratedData = {
        partners: partnersResult.data,
        customerCategories: customerCategoriesResult.data,
        supplierCategories: supplierCategoriesResult.data,
      };

      setData(newData);

      // 顯示所有夥伴（因為已重置到 'all' 標籤頁）
      setFilteredPartners(partnersResult.data);

      // 計算統計資料
      calculateStats(partnersResult.data);

      partnerPageLogger.log(
        SYMBOLS.SUCCESS,
        "Partner 模組資料載入完成，所有篩選條件已重置"
      );
      message.success("資料重新載入完成，篩選條件已清除");
    } catch (error) {
      partnerPageLogger.log(
        SYMBOLS.ERROR,
        "載入 Partner 模組資料時發生錯誤:",
        error
      );
      message.error("載入資料失敗，請重試");
    } finally {
      setLoading(false);
    }
  }, [calculateStats, clearTagFilters]);

  // Initial load
  useEffect(() => {
    // 頁面重新載入時重置所有篩選狀態
    setTagFilters({});
    setActiveTab("all");
    setCurrentPage(1);

    loadAllData();
  }, [loadAllData]);

  // Partner 篩選邏輯
  const applyPartnerFilters = useCallback(
    (
      partners: Partner[],
      searchText: string,
      activeFilters: string[],
      filterValues: Record<string, any>
    ): Partner[] => {
      if (!Array.isArray(partners)) {
        partnerPageLogger.log(SYMBOLS.WARNING, "partners 不是陣列:", partners);
        return [];
      }

      return partners.filter((partner) => {
        if (!partner) return false;

        // 搜尋文字篩選
        const matchesSearch =
          !searchText ||
          safeString(partner.partnerID)
            .toLowerCase()
            .includes(searchText.toLowerCase()) ||
          (partner.individualDetail &&
            safeString(partner.individualDetail.lastName)
              .toLowerCase()
              .includes(searchText.toLowerCase())) ||
          (partner.individualDetail &&
            safeString(partner.individualDetail.firstName)
              .toLowerCase()
              .includes(searchText.toLowerCase())) ||
          (partner.enterpriseDetail &&
            safeString(partner.enterpriseDetail.companyName)
              .toLowerCase()
              .includes(searchText.toLowerCase()));

        // 動態篩選條件
        const matchesFilters = activeFilters.every((filterKey) => {
          const value = filterValues[filterKey];
          if (!value) return true;

          try {
            switch (filterKey) {
              case "role":
                return Array.isArray(value)
                  ? value.some(
                      (v) =>
                        (v === "customer" && partner.customerDetail) ||
                        (v === "supplier" && partner.supplierDetail)
                    )
                  : (value === "customer" && partner.customerDetail) ||
                      (value === "supplier" && partner.supplierDetail);

              case "customerCategory":
                if (!partner.customerDetail) return false;
                return Array.isArray(value)
                  ? value.includes(partner.customerDetail.customerCategoryID)
                  : value === partner.customerDetail.customerCategoryID;

              case "supplierCategory":
                if (!partner.supplierDetail) return false;
                return Array.isArray(value)
                  ? value.includes(partner.supplierDetail.supplierCategoryID)
                  : value === partner.supplierDetail.supplierCategoryID;

              default:
                return true;
            }
          } catch (error) {
            partnerPageLogger.log(
              SYMBOLS.WARNING,
              `篩選條件 ${filterKey} 處理錯誤:`,
              error
            );
            return true;
          }
        });

        // 標籤篩選條件
        const matchesTagFilters = Object.entries(tagFilters).every(
          ([filterType, filterValue]) => {
            if (!filterValue) return true;

            switch (filterType) {
              case "type":
                if (filterValue === "individual") {
                  return !!partner.individualDetail;
                } else if (filterValue === "enterprise") {
                  return !!partner.enterpriseDetail;
                }
                return true;

              case "role":
                if (filterValue === "customer") {
                  return !!partner.customerDetail;
                } else if (filterValue === "supplier") {
                  return !!partner.supplierDetail;
                }
                return true;

              case "category":
                return (
                  partner.customerDetail?.customerCategory?.name ===
                    filterValue ||
                  partner.supplierDetail?.supplierCategory?.name === filterValue
                );

              default:
                return true;
            }
          }
        );

        return matchesSearch && matchesFilters && matchesTagFilters;
      });
    },
    [data.customerCategories, data.supplierCategories, tagFilters]
  );

  // 移動端卡片渲染函數 - 使用useCallback避免不必要的重新渲染
  const mobileCardRender = useCallback(({
    record: partner,
  }: {
    record: Partner;
    actions?: React.ReactNode;
  }) => (
    <div>
      <div style={{ marginBottom: 12 }}>
        <Typography.Text strong style={{ fontSize: "16px", display: "block" }}>
          {partner.enterpriseDetail?.companyName ||
            `${partner.individualDetail?.lastName}${partner.individualDetail?.firstName}` ||
            "-"}
        </Typography.Text>
        <div style={{ marginTop: 4 }}>
          <Tag
            color={partner.individualDetail ? "blue" : "green"}
            icon={
              partner.individualDetail ? <UserOutlined /> : <TeamOutlined />
            }
            style={{ cursor: "pointer" }}
          >
            {partner.individualDetail ? "個人" : "法人"}
          </Tag>
          <Tag
            color={safeBoolean(partner.isStop) ? "red" : "green"}
            icon={
              safeBoolean(partner.isStop) ? (
                <StopOutlined />
              ) : (
                <CheckCircleOutlined />
              )
            }
            style={{ marginLeft: 4 }}
          >
            {safeBoolean(partner.isStop) ? "停用" : "啟用"}
          </Tag>
        </div>
      </div>

      <Space direction="vertical" size="small" style={{ width: "100%" }}>
        <div>
          <Typography.Text type="secondary" style={{ fontSize: "12px" }}>
            角色：
          </Typography.Text>
          <Space size="small">
            {partner.customerDetail && (
              <Tag
                color="cyan"
                icon={<ContactsOutlined />}
                style={{ cursor: "pointer" }}
              >
                客戶
              </Tag>
            )}
            {partner.supplierDetail && (
              <Tag
                color="orange"
                icon={<ShopOutlined />}
                style={{ cursor: "pointer" }}
              >
                供應商
              </Tag>
            )}
            {!partner.customerDetail && !partner.supplierDetail && (
              <Tag color="default">無角色</Tag>
            )}
          </Space>
        </div>
        {(partner.customerDetail?.customerCategory ||
          partner.supplierDetail?.supplierCategory) && (
          <div>
            <Typography.Text type="secondary" style={{ fontSize: "12px" }}>
              分類：
            </Typography.Text>
            <Space direction="vertical" size="small">
              {partner.customerDetail?.customerCategory && (
                <Tag color="blue" style={{ cursor: "pointer" }}>
                  客戶: {partner.customerDetail.customerCategory.name}
                </Tag>
              )}
              {partner.supplierDetail?.supplierCategory && (
                <Tag color="orange" style={{ cursor: "pointer" }}>
                  供應商: {partner.supplierDetail.supplierCategory.name}
                </Tag>
              )}
            </Space>
          </div>
        )}
      </Space>
    </div>
  ), []);

  // Partner 篩選選項 - 移除法人類型區分，僅保留業務角色分類
  const partnerFilterOptions: FilterOption[] = useMemo(
    () => [
      {
        label: "業務角色",
        value: "role",
        type: "select",
        children: [
          { label: "客戶", value: "customer" },
          { label: "供應商", value: "supplier" },
        ],
      },
      {
        label: "客戶分類",
        value: "customerCategory",
        type: "treeSelect",
        treeData: convertCustomerCategoriesToTreeData(
          buildCustomerCategoryTree(data.customerCategories)
        ),
      },
      {
        label: "供應商分類",
        value: "supplierCategory",
        type: "treeSelect",
        treeData: convertSupplierCategoriesToTreeData(
          buildSupplierCategoryTree(data.supplierCategories)
        ),
      },
    ],
    [data.customerCategories, data.supplierCategories]
  );

  // CRUD Operations
  const handleSubmit = useCallback(async (values: any) => {
    setLoading(true);
    try {
      // 根據新增或編輯使用不同的資料準備函數
      const submissionData = selectedPartner
        ? preparePartnerEditData({ ...values, partnerID: selectedPartner.partnerID })
        : preparePartnerAddData(values);

      const response = selectedPartner
        ? await editPartner(submissionData)
        : await addPartner(submissionData);

      if (response.success) {
        message.success(selectedPartner ? "更新成功" : "新增成功");
        setIsModalVisible(false);
        setSelectedPartner(null);
        await loadAllData();
      } else {
        message.error(response.message || "操作失敗");
      }
    } catch (error) {
      partnerPageLogger.log(
        SYMBOLS.ERROR,
        "提交商業夥伴資料時發生錯誤:",
        error
      );
      message.error("操作失敗，請重試");
    } finally {
      setLoading(false);
    }
  }, [selectedPartner, loadAllData]);

  const handleEdit = useCallback((partner: Partner) => {
    setSelectedPartner(partner);
    setIsModalVisible(true);
  }, []);

  const handleDelete = useCallback(async (partnerId: string) => {
    Modal.confirm({
      title: "確認刪除",
      content: "確定要刪除此商業夥伴嗎？此操作無法復原。",
      okText: "確定刪除",
      cancelText: "取消",
      okType: "danger",
      onOk: async () => {
        const response = await deletePartner(partnerId);
        if (response.success) {
          message.success("刪除成功");
          await loadAllData();
        } else {
          message.error(response.message || "刪除失敗");
        }
      },
    });
  }, [loadAllData]);

  // 聯絡人管理處理函數
  const handleOpenContactManagement = useCallback(() => {
    setIsContactManagementVisible(true);
  }, []);

  const handleContactCreate = useCallback((contact: Contact) => {
    message.success(`聯絡人新增成功：${contact.name}`);
    setIsContactManagementVisible(false);
  }, []);

  // Table columns
  const columns = useMemo(
    () => [
      {
        title: "夥伴名稱",
        key: "name",
        sorter: (a: Partner, b: Partner) =>
          safeString(
            a.enterpriseDetail?.companyName || a.individualDetail?.lastName
          ).localeCompare(
            safeString(
              b.enterpriseDetail?.companyName || b.individualDetail?.lastName
            )
          ),
        render: (record: Partner) => (
          <span
            style={{ fontWeight: 500, color: "#1890ff", cursor: "pointer" }}
            onClick={() => handleEdit(record)}
          >
            {record.enterpriseDetail?.companyName ||
              `${record.individualDetail?.lastName}${record.individualDetail?.firstName}` ||
              "-"}
          </span>
        ),
      },
      {
        title: "類型",
        key: "type",
        width: 100,
        render: (record: Partner) => (
          <Tag
            color={record.individualDetail ? "blue" : "green"}
            icon={record.individualDetail ? <UserOutlined /> : <TeamOutlined />}
          >
            {record.individualDetail ? "個人" : "法人"}
          </Tag>
        ),
      },
      {
        title: "角色",
        key: "role",
        width: 120,
        render: (record: Partner) => (
          <Space direction="vertical" size="small">
            {record.customerDetail && (
              <Tag color="cyan" icon={<ContactsOutlined />}>客戶</Tag>
            )}
            {record.supplierDetail && (
              <Tag color="orange" icon={<ShopOutlined />}>供應商</Tag>
            )}
            {!record.customerDetail && !record.supplierDetail && (
              <Tag color="default">無</Tag>
            )}
          </Space>
        ),
      },
      {
        title: "狀態",
        dataIndex: "isStop",
        key: "isStop",
        width: 80,
        render: (isStop: boolean) => (
          <Tag
            color={safeBoolean(isStop) ? "red" : "green"}
            icon={
              safeBoolean(isStop) ? <StopOutlined /> : <CheckCircleOutlined />
            }
          >
            {safeBoolean(isStop) ? "停用" : "啟用"}
          </Tag>
        ),
      },
      {
        title: "分類",
        key: "category",
        width: 150,
        render: (record: Partner) => (
          <Space direction="vertical" size="small">
            {record.customerDetail?.customerCategory && (
              <Tag color="blue">
                客戶: {record.customerDetail.customerCategory.name}
              </Tag>
            )}
            {record.supplierDetail?.supplierCategory && (
              <Tag color="orange">
                供應商: {record.supplierDetail.supplierCategory.name}
              </Tag>
            )}
          </Space>
        ),
      },
      {
        title: "操作",
        key: "actions",
        width: 100,
        fixed: "right" as const,
        render: (record: Partner) => (
          <Space size="small">
            <Tooltip title="編輯">
              <Button
                type="primary"
                size="small"
                icon={<EditOutlined />}
                onClick={() => handleEdit(record)}
                className="unified-button"
              />
            </Tooltip>
            <Popconfirm
              title="確定要刪除此商業夥伴嗎？"
              description="此操作無法復原"
              onConfirm={() => handleDelete(record.partnerID)}
              okText="確定"
              cancelText="取消"
              okType="danger"
            >
              <Tooltip title="刪除">
                <Button
                  danger
                  size="small"
                  icon={<DeleteOutlined />}
                  className="unified-button"
                />
              </Tooltip>
            </Popconfirm>
          </Space>
        ),
      },
    ],
    [handleEdit, handleDelete]
  );

  return (
    <div className="p-6">
      <ResponsiveStyles />
      <Spin spinning={loading}>
        <div style={{ marginBottom: "24px" }}>
          <Title
            level={2}
            style={{
              margin: 0,
              display: "flex",
              alignItems: "center",
              gap: "12px",
            }}
          >
            <ContactsOutlined style={{ color: "#1890ff" }} />
            商業夥伴管理
          </Title>
          <Typography.Text type="secondary">
            管理所有商業夥伴資料
          </Typography.Text>
        </div>

        {/* 響應式統計卡片 */}
        <Row gutter={[16, 16]} style={{ marginBottom: "24px" }}>
          <Col xs={24} sm={24} lg={16}>
            <Card
              size={isMobile ? "small" : "default"}
              title={
                <div
                  style={{ display: "flex", alignItems: "center", gap: "8px" }}
                >
                  <TeamOutlined style={{ color: "#1890ff" }} />
                  <span>商業夥伴統計</span>
                </div>
              }
              className="unified-card"
              styles={{
                header: {
                  backgroundColor: "#fafafa",
                  borderBottom: "1px solid #f0f0f0",
                  padding: isMobile ? "12px 16px" : "16px 20px",
                },
                body: {
                  padding: isMobile ? "12px 16px" : "16px 20px",
                },
              }}
            >
              <Row gutter={[16, 16]} justify="center">
                <Col xs={24} lg={8}>
                  <Statistic
                    title="客戶數量"
                    value={stats.customerCount}
                    prefix={<ContactsOutlined />}
                    valueStyle={{
                      fontSize: isMobile ? "18px" : "20px",
                      color: "#fa8c16",
                    }}
                  />
                </Col>
                <Col xs={24} lg={8}>
                  <Statistic
                    title="供應商數量"
                    value={stats.supplierCount}
                    prefix={<ShopOutlined />}
                    valueStyle={{
                      fontSize: isMobile ? "18px" : "20px",
                      color: "#13c2c2",
                    }}
                  />
                </Col>
                <Col xs={24} lg={8}>
                  <Statistic
                    title="客戶+供應商"
                    value={
                      data.partners.filter(
                        (p) => p.customerDetail && p.supplierDetail
                      ).length
                    }
                    prefix={<SettingOutlined />}
                    valueStyle={{
                      fontSize: isMobile ? "18px" : "20px",
                      color: "#eb2f96",
                    }}
                  />
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>

        {/* 管理操作卡片 */}
        <Row gutter={[16, 16]} style={{ marginBottom: "16px" }}>
          <Col xs={24} sm={8}>
            <Card
              size={isMobile ? "small" : "default"}
              title={
                <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
                  <SettingOutlined style={{ color: "#722ed1" }} />
                  <span>分類管理</span>
                </div>
              }
              className="unified-card"
              styles={{
                header: {
                  backgroundColor: "#fafafa",
                  borderBottom: "1px solid #f0f0f0",
                  padding: isMobile ? "12px 16px" : "16px 20px",
                },
                body: {
                  padding: isMobile ? "12px 16px" : "16px 20px",
                },
              }}
            >
              <Row gutter={[8, 8]}>
                <Col xs={24}>
                  <Button
                    type="primary"
                    icon={<TagsOutlined />}
                    onClick={() => setIsCustomerCategoryModalVisible(true)}
                    className="unified-button"
                    style={{ width: "100%" }}
                  >
                    客戶分類管理
                  </Button>
                </Col>
                <Col xs={24}>
                  <Button
                    type="primary"
                    icon={<TagsOutlined />}
                    onClick={() => setIsSupplierCategoryModalVisible(true)}
                    className="unified-button"
                    style={{ width: "100%" }}
                  >
                    供應商分類管理
                  </Button>
                </Col>
              </Row>
            </Card>
          </Col>
          <Col xs={24} sm={8}>
            <Card
              size={isMobile ? "small" : "default"}
              title={
                <div style={{ display: "flex", alignItems: "center", gap: "8px" }}>
                  <ContactsOutlined style={{ color: "#52c41a" }} />
                  <span>聯絡人管理</span>
                </div>
              }
              className="unified-card"
              styles={{
                header: {
                  backgroundColor: "#fafafa",
                  borderBottom: "1px solid #f0f0f0",
                  padding: isMobile ? "12px 16px" : "16px 20px",
                },
                body: {
                  padding: isMobile ? "12px 16px" : "16px 20px",
                },
              }}
            >
              <Row gutter={[8, 8]}>
                <Col xs={24}>
                  <Button
                    type="primary"
                    icon={<ContactsOutlined />}
                    onClick={handleOpenContactManagement}
                    className="unified-button"
                    style={{ width: "100%" }}
                  >
                    聯絡人管理
                  </Button>
                </Col>
              </Row>
            </Card>
          </Col>
        </Row>

        {/* 動態搜尋和篩選 */}
        <FilterSearchContainer
          ref={filterSearchRef}
          title="篩選與搜尋"
          filterOptions={partnerFilterOptions}
          searchPlaceholder={isMobile ? "搜尋夥伴" : "搜尋夥伴名稱、編號"}
          showStats={true}
          stats={{
            total: filterPartnersByTab(data.partners, activeTab).length,
            filtered: filteredPartners.length,
          }}
          showClearMessage={true}
          clearMessage="已清除所有商業夥伴篩選條件"
          onFilterResult={useCallback((state) => {
            // 先根據標籤頁篩選
            const tabFiltered = filterPartnersByTab(data.partners, activeTab);

            // 再應用搜尋和進階篩選
            const filtered = applyPartnerFilters(
              tabFiltered,
              state.searchText,
              state.activeFilters,
              state.filterValues
            );
            setFilteredPartners(filtered);
            setCurrentPage(1); // 重置分頁

            // 篩選結果已更新
          }, [data.partners, activeTab, filterPartnersByTab, applyPartnerFilters])}
          compact={isMobile}
          className="mb-6"
        />

        {/* 商業夥伴列表 */}
        <Card
          title={
            <div
              style={{
                display: "flex",
                alignItems: "center",
                justifyContent: "flex-start",
                width: "100%",
                flexWrap: isMobile ? "wrap" : "nowrap",
                gap: "12px",
              }}
            >
              <div
                style={{ display: "flex", alignItems: "center", gap: "8px" }}
              >
                <UnorderedListOutlined />
                <span>商業夥伴列表</span>
                <Tag color="blue">{filteredPartners.length} 項</Tag>
              </div>

              {/* 主要操作按鈕 - 左對齊到標題列 */}
              <div
                style={{
                  display: "flex",
                  alignItems: "center",
                  gap: "8px",
                  flexWrap: "wrap",
                  marginTop: isMobile ? "8px" : "0",
                  marginLeft: isMobile ? "0" : "16px",
                }}
              >
                <Button
                  icon={<SyncOutlined />}
                  onClick={loadAllData}
                  loading={loading}
                  size="small"
                  title="重新載入資料並清除所有篩選條件"
                  className="unified-button"
                >
                  {!isMobile && "重新載入"}
                </Button>
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => {
                    setSelectedPartner(null);
                    setIsModalVisible(true);
                  }}
                  size="small"
                  className="unified-button"
                >
                  {isMobile ? "新增" : "新增夥伴"}
                </Button>
              </div>
            </div>
          }
          className="enhanced-table unified-card"
          styles={{
            body: { padding: isMobile ? "8px" : "24px" },
            header: {
              padding: isMobile ? "12px 16px" : "16px 24px",
              borderBottom: "1px solid #f0f0f0",
              backgroundColor: "#fafafa",
            },
          }}
        >
          <ResponsiveTable
            columns={columns}
            dataSource={filteredPartners}
            rowKey="partnerID"
            loading={loading}
            mobileCardRender={mobileCardRender}
            pagination={{
              current: currentPage,
              pageSize: pageSize,
              total: filteredPartners.length,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => {
                const isFiltered =
                  filteredPartners.length !== data.partners.length;
                return isFiltered
                  ? `第 ${range[0]}-${range[1]} 項，共 ${total} 項 (已篩選，原始資料 ${data.partners.length} 項)`
                  : `第 ${range[0]}-${range[1]} 項，共 ${total} 項`;
              },
              pageSizeOptions: ["10", "20", "50", "100"],
              onChange: useCallback((page: number, size: number) => {
                partnerPageLogger.log(
                  SYMBOLS.INFO,
                  `分頁變更: 第 ${page} 頁，每頁 ${size} 筆`
                );
                setCurrentPage(page);
                if (size !== pageSize) {
                  setPageSize(size);
                  setCurrentPage(1);
                }
              }, [pageSize]),
              onShowSizeChange: useCallback((_: number, size: number) => {
                partnerPageLogger.log(SYMBOLS.INFO, `每頁筆數變更: ${size} 筆`);
                setPageSize(size);
                setCurrentPage(1);
              }, []),
            }}
          />
        </Card>
      </Spin>

      <PartnerFormModal
        visible={isModalVisible}
        onClose={() => {
          setIsModalVisible(false);
          setSelectedPartner(null);
        }}
        selectedPartner={selectedPartner}
        onSubmit={handleSubmit}
        loading={loading}
        customerCategories={data.customerCategories}
        supplierCategories={data.supplierCategories}
        onCategoryDataChange={loadAllData}
      />

      {/* 客戶分類管理 Modal */}
      <CustomerCategoryAdapter
        visible={isCustomerCategoryModalVisible}
        onClose={() => setIsCustomerCategoryModalVisible(false)}
        categories={data.customerCategories}
        onDataChange={loadAllData}
      />

      {/* 供應商分類管理 Modal */}
      <SupplierCategoryAdapter
        visible={isSupplierCategoryModalVisible}
        onClose={() => setIsSupplierCategoryModalVisible(false)}
        categories={data.supplierCategories}
        onDataChange={loadAllData}
      />

      {/* 聯絡人管理中心 */}
      <Modal
        title="聯絡人管理"
        open={isContactManagementVisible}
        onCancel={() => setIsContactManagementVisible(false)}
        footer={null}
        width="90%"
        style={{ maxWidth: '1200px' }}
        destroyOnClose
      >
        <ResponsiveContactManagementCenter
          mode="standalone"
          onClose={() => setIsContactManagementVisible(false)}
          onContactCreate={handleContactCreate}
        />
      </Modal>
    </div>
  );
};

export default PartnerPage;
