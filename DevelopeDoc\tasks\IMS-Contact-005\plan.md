# IMS-Contact-005 實作規劃

## 實作概述

本任務專注於優化 Contact 與 PartnerContact 的整合操作流程，解決現有的操作複雜性和資料同步問題。基於 IMS-Contact-004 的完成狀況，重點改善用戶體驗和確保交易完整性。

## 實作階段

### 階段一：後端 API 優化與交易完整性支援

#### 1.1 修正現有 API 路由問題

**目標**：修正 PartnerContactController 的路由和 HTTP 方法設計問題

**問題分析**：
- ❌ 重複的模組前綴：`[Route("api/ims/[controller]")]` 會產生 `api/ims/ims/PartnerContact`
- ❌ 不符合 RESTful 原則：`[HttpGet("{PartnerID}/{ContactID}")]` 使用複合參數
- ❌ 方法命名不夠明確：缺乏業務語義
- ❌ 前後端路由不一致：需要同步更新前端 api.ts

**修正內容**：

```csharp
// ✅ 修正後的路由設計
[ApiController]
[Route("api/[controller]")]  // 使用預設路由，讓 ModuleRouteConvention 自動處理為 api/ims/PartnerContact
[SwaggerTag("商業夥伴聯絡人管理")]
public class PartnerContactController : ControllerBase
{
    // ✅ 查詢夥伴的所有聯絡人
    [HttpGet("partner/{partnerId}")]
    [SwaggerOperation(Summary = "取得夥伴的所有聯絡人", Description = "取得指定夥伴的所有聯絡人關聯")]
    public async Task<IActionResult> GetByPartner(Guid partnerId)
    
    // ✅ 查詢特定聯絡人關聯
    [HttpGet("{partnerContactId}")]
    [SwaggerOperation(Summary = "取得特定聯絡人關聯", Description = "根據關聯ID取得聯絡人關聯詳情")]
    public async Task<IActionResult> Get(Guid partnerContactId)
    
    // ✅ 新增聯絡人關聯
    [HttpPost("associate")]
    [SwaggerOperation(Summary = "關聯聯絡人到夥伴", Description = "將現有聯絡人關聯到指定夥伴")]
    public async Task<IActionResult> Associate([FromBody] AssociateContactRequest request)
    
    // ✅ 更新聯絡人關聯
    [HttpPut("update")]
    [SwaggerOperation(Summary = "更新聯絡人關聯", Description = "更新聯絡人與夥伴的關聯資訊")]
    public async Task<IActionResult> Update([FromBody] UpdateContactAssociationRequest request)
    
    // ✅ 取消聯絡人關聯
    [HttpDelete("unassociate/{partnerContactId}")]
    [SwaggerOperation(Summary = "取消聯絡人關聯", Description = "移除聯絡人與夥伴的關聯")]
    public async Task<IActionResult> Unassociate(Guid partnerContactId)
    
    // ✅ 一鍵新增聯絡人並關聯（新增）
    [HttpPost("add-and-associate")]
    [SwaggerOperation(Summary = "新增聯絡人並關聯", Description = "一次性新增聯絡人並建立夥伴關聯")]
    public async Task<IActionResult> AddAndAssociate([FromBody] AddContactAndAssociateRequest request)
}
```

#### 1.2 新增 PartnerService 方法

**目標**：提供一次性新增聯絡人並建立關聯的後端支援

**實作內容**：

```csharp
// 在 PartnerService.cs 中新增
public async Task<(bool, string)> AddContactAndAssociateAsync(AddContactAndAssociateRequest request)
{
    using var transaction = new TransactionScope(TransactionScopeAsyncFlowOption.Enabled);
    
    try
    {
        // 1. 新增聯絡人
        var contactEntity = new Contact
        {
            ContactID = Guid.NewGuid(),
            Name = request.Contact.Name,
            Position = request.Contact.Position,
            Email = request.Contact.Email,
            Phone = request.Contact.Phone,
            IsActive = request.Contact.IsActive,
            ContactType = request.Contact.ContactType,
            Department = request.Contact.Department,
            Company = request.Contact.Company
            // 審計欄位由攔截器自動處理
        };
        
        _context.Ims_Contact.Add(contactEntity);
        await _context.SaveChangesAsync();
        
        // 2. 建立 PartnerContact 關聯
        var partnerContactEntity = new PartnerContact
        {
            PartnerContactID = Guid.NewGuid(),
            PartnerID = request.PartnerID,
            ContactID = contactEntity.ContactID,
            Role = request.PartnerContact.Role,
            Priority = request.PartnerContact.Priority,
            ProfessionalField = request.PartnerContact.ProfessionalField,
            Remarks = request.PartnerContact.Remarks,
            BusinessScope = request.PartnerContact.BusinessScope
            // 審計欄位由攔截器自動處理
        };
        
        _context.Ims_PartnerContact.Add(partnerContactEntity);
        await _context.SaveChangesAsync();
        
        transaction.Complete();
        
        await _logger.LogInfoAsync($"聯絡人新增並關聯成功: ContactID={contactEntity.ContactID}, PartnerID={request.PartnerID}", nameof(PartnerService));
        
        return (true, "聯絡人新增並關聯成功");
    }
    catch (Exception ex)
    {
        await _logger.LogErrorAsync($"聯絡人新增並關聯失敗: {ex.Message}", ex, nameof(PartnerService));
        throw;
    }
}
```

#### 1.3 請求模型設計

**目標**：建立清晰的請求模型，遵循開發準則

**實作內容**：

```csharp
// ✅ 新增聯絡人並關聯請求
public class AddContactAndAssociateRequest
{
    [Required]
    public Guid PartnerID { get; set; }
    
    [Required]
    public ContactDTO Contact { get; set; }
    
    [Required]
    public PartnerContactDTO PartnerContact { get; set; }
}

// ✅ 關聯聯絡人請求
public class AssociateContactRequest
{
    [Required]
    public Guid PartnerID { get; set; }
    
    [Required]
    public Guid ContactID { get; set; }
    
    [Required]
    public PartnerContactDTO PartnerContact { get; set; }
}

// ✅ 更新關聯請求
public class UpdateContactAssociationRequest
{
    [Required]
    public Guid PartnerContactID { get; set; }
    
    [Required]
    public PartnerContactDTO PartnerContact { get; set; }
}
```

#### 1.4 前後端路由同步

**目標**：確保前後端 API 路由完全一致

**前端 API 路由更新**：
```typescript
// 在 src/config/api.ts 中新增或更新
// PartnerContact 商業夥伴聯絡人關聯管理
getPartnerContactList: `${API_BASE_URL}/${MODULE_IMS_URL}/PartnerContact`,            // api/ims/PartnerContact
getPartnerContactByPartner: `${API_BASE_URL}/${MODULE_IMS_URL}/PartnerContact/partner`, // api/ims/PartnerContact/partner/{partnerId}
getPartnerContact: `${API_BASE_URL}/${MODULE_IMS_URL}/PartnerContact`,                // api/ims/PartnerContact/{partnerContactId}
associateContact: `${API_BASE_URL}/${MODULE_IMS_URL}/PartnerContact/associate`,       // api/ims/PartnerContact/associate
updateContactAssociation: `${API_BASE_URL}/${MODULE_IMS_URL}/PartnerContact/update`,  // api/ims/PartnerContact/update
unassociateContact: `${API_BASE_URL}/${MODULE_IMS_URL}/PartnerContact/unassociate`,   // api/ims/PartnerContact/unassociate/{partnerContactId}
addContactAndAssociate: `${API_BASE_URL}/${MODULE_IMS_URL}/PartnerContact/add-and-associate`, // api/ims/PartnerContact/add-and-associate
```

#### 1.5 遵循開發準則的 API 設計

**目標**：確保 API 設計完全符合開發準則

**遵循原則**：
- ✅ **路由設計**：使用 `[Route("api/[controller]")]` 格式，讓 ModuleRouteConvention 自動處理
- ✅ **HTTP 方法**：遵循 RESTful 原則，使用明確的動詞
- ✅ **參數設計**：所有參數在 Body 中，避免 URL 複雜度
- ✅ **錯誤處理**：統一的錯誤回應格式
- ✅ **日誌記錄**：使用 `nameof` 運算子
- ✅ **審計欄位**：由攔截器自動處理，不手動設定
- ✅ **前後端同步**：確保前端 api.ts 與後端路由一致

### 階段二：前端整合優化

#### 2.1 建立 ContactContext

**目標**：統一聯絡人狀態管理，減少重複載入

**實作內容**：

```typescript
// src/contexts/ContactContext.tsx
interface ContactContextType {
  contacts: Contact[];
  loading: boolean;
  error: string | null;
  refreshContacts: () => Promise<void>;
  addContact: (contact: Contact) => Promise<void>;
  updateContact: (contact: Contact) => Promise<void>;
  deleteContact: (contactId: string) => Promise<void>;
}

const ContactContext = createContext<ContactContextType | undefined>(undefined);

export const ContactProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const refreshContacts = async () => {
    setLoading(true);
    try {
      const response = await getContactList();
      if (response.success && response.data) {
        setContacts(response.data);
        setError(null);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : '載入聯絡人失敗');
    } finally {
      setLoading(false);
    }
  };

  // ... 其他方法實作

  return (
    <ContactContext.Provider value={{
      contacts,
      loading,
      error,
      refreshContacts,
      addContact,
      updateContact,
      deleteContact
    }}>
      {children}
    </ContactContext.Provider>
  );
};

export const useContactContext = () => {
  const context = useContext(ContactContext);
  if (!context) {
    throw new Error('useContactContext must be used within ContactProvider');
  }
  return context;
};
```

#### 2.2 建立 ContactManagementCenter

**目標**：統一操作入口，支援多種使用場景

**實作內容**：

```typescript
// src/app/ims/components/shared/ContactManagementCenter.tsx
interface ContactManagementCenterProps {
  mode: 'standalone' | 'partner-integrated';
  partnerID?: string;
  onContactSelect?: (contact: Contact) => void;
  onContactCreate?: (contact: Contact, partnerContact?: PartnerContact) => void;
  onClose?: () => void;
}

const ContactManagementCenter: React.FC<ContactManagementCenterProps> = ({
  mode,
  partnerID,
  onContactSelect,
  onContactCreate,
  onClose
}) => {
  const { contacts, loading, refreshContacts } = useContactContext();
  const [showForm, setShowForm] = useState(false);
  const [formMode, setFormMode] = useState<'create' | 'quick' | 'edit'>('create');

  // 一鍵新增聯絡人並關聯
  const handleQuickAddAndAssociate = async (contactData: Contact, partnerContactData: PartnerContact) => {
    try {
      if (mode === 'partner-integrated' && partnerID) {
        // 使用後端 API 一次性完成
        const response = await addContactAndAssociate(partnerID, contactData, partnerContactData);
        if (response.success) {
          message.success('聯絡人新增並關聯成功');
          await refreshContacts();
          onContactCreate?.(contactData, partnerContactData);
          onClose?.();
        }
      } else {
        // 分步操作
        const contactResponse = await addContact(contactData);
        if (contactResponse.success) {
          message.success('聯絡人新增成功');
          await refreshContacts();
          onContactCreate?.(contactData);
          onClose?.();
        }
      }
    } catch (error) {
      message.error('操作失敗：' + (error instanceof Error ? error.message : '未知錯誤'));
    }
  };

  return (
    <div className="contact-management-center">
      {/* 操作按鈕區域 */}
      <div className="action-buttons">
        <Button 
          type="primary" 
          onClick={() => {
            setFormMode('create');
            setShowForm(true);
          }}
        >
          新增聯絡人
        </Button>
        
        {mode === 'partner-integrated' && (
          <Button 
            onClick={() => {
              setFormMode('quick');
              setShowForm(true);
            }}
          >
            快速新增並關聯
          </Button>
        )}
      </div>

      {/* 聯絡人列表 */}
      <ContactTable 
        contacts={contacts}
        loading={loading}
        onEdit={(contact) => {
          setFormMode('edit');
          setShowForm(true);
        }}
        onSelect={onContactSelect}
      />

      {/* 表單 Modal */}
      {showForm && (
        <ContactFormModal
          visible={showForm}
          mode={formMode}
          partnerID={partnerID}
          onClose={() => setShowForm(false)}
          onSubmit={handleQuickAddAndAssociate}
        />
      )}
    </div>
  );
};
```

#### 2.3 優化現有組件

**目標**：整合 ContactContext 和 ContactManagementCenter

**實作內容**：

```typescript
// 更新 ContactManagementTab.tsx
const ContactManagementTab: React.FC<ContactManagementTabProps> = ({ partnerID }) => {
  const { contacts, refreshContacts } = useContactContext();
  const [showManagementCenter, setShowManagementCenter] = useState(false);

  return (
    <div className="contact-management-tab">
      {/* 使用 ContactManagementCenter */}
      <ContactManagementCenter
        mode="partner-integrated"
        partnerID={partnerID}
        onContactCreate={async (contact, partnerContact) => {
          await refreshContacts();
          // 更新 Partner 資料
        }}
        onClose={() => setShowManagementCenter(false)}
      />
    </div>
  );
};

// 更新 PartnerFormModal.tsx
const PartnerFormModal: React.FC<PartnerFormModalProps> = ({ ... }) => {
  return (
    <Modal>
      {/* 其他表單內容 */}
      
      <Tabs>
        <TabPane tab="聯絡人管理" key="contacts">
          <ContactManagementCenter
            mode="partner-integrated"
            partnerID={selectedPartner?.partnerID}
            onContactCreate={handleContactCreate}
          />
        </TabPane>
      </Tabs>
    </Modal>
  );
};
```

### 階段三：用戶體驗優化

#### 3.1 操作流程統一

**目標**：消除功能重複，提供一致的操作體驗

**實作內容**：

```typescript
// 統一的操作流程
const UnifiedContactOperations = () => {
  const [operationMode, setOperationMode] = useState<'standalone' | 'partner-integrated'>('standalone');
  
  return (
    <div className="unified-contact-operations">
      {/* 模式選擇 */}
      <div className="mode-selector">
        <Radio.Group value={operationMode} onChange={(e) => setOperationMode(e.target.value)}>
          <Radio value="standalone">獨立管理</Radio>
          <Radio value="partner-integrated">夥伴整合</Radio>
        </Radio.Group>
      </div>

      {/* 統一的操作介面 */}
      <ContactManagementCenter
        mode={operationMode}
        onContactCreate={handleContactCreate}
      />
    </div>
  );
};
```

#### 3.2 響應式優化

**目標**：改進移動端操作體驗

**實作內容**：

```typescript
// 響應式設計優化
const ContactManagementCenter: React.FC<ContactManagementCenterProps> = ({ ... }) => {
  const { screenSize } = useScreenSize();
  const isMobile = screenSize === 'mobile';

  return (
    <div className={`contact-management-center ${isMobile ? 'mobile' : ''}`}>
      {/* 移動端優化佈局 */}
      {isMobile ? (
        <div className="mobile-layout">
          <div className="action-buttons-mobile">
            <Button type="primary" block>新增聯絡人</Button>
            <Button block>快速新增並關聯</Button>
          </div>
          
          <div className="contact-list-mobile">
            {/* 移動端聯絡人列表 */}
          </div>
        </div>
      ) : (
        <div className="desktop-layout">
          {/* 桌面端佈局 */}
        </div>
      )}
    </div>
  );
};
```

## 驗收標準

### 功能驗收
- [ ] 一鍵新增聯絡人並關聯功能正常運作
- [ ] 交易完整性測試通過，無資料不一致
- [ ] 所有操作流程統一，無重複功能
- [ ] 資料同步正常，多組件狀態一致

### 用戶體驗驗收
- [ ] 操作流程順暢，步驟簡化 50% 以上
- [ ] 錯誤處理完善，提供清晰指引
- [ ] 響應式設計在所有斷點正常
- [ ] 載入狀態和成功提示統一

### 技術品質驗收
- [ ] 代碼重用率提升，減少重複代碼
- [ ] TypeScript 類型安全，無編譯錯誤
- [ ] 符合 FastERP 編碼規範
- [ ] 文檔完整準確

## 風險緩解

### 技術風險
- **後端變更風險**：新增 API 不影響現有功能，使用 TransactionScope 確保資料一致性
- **前端整合複雜度**：分階段實施，逐步整合現有組件
- **用戶體驗變化**：保持核心操作流程，優化細節體驗

### 業務風險
- **資料一致性**：使用後端交易確保操作原子性
- **操作複雜度**：提供智能操作指引和預設值
- **學習成本**：保持操作流程直觀，提供操作教學

## 時程規劃

| 階段 | 工作天 | 主要任務 | 交付物 |
|------|--------|----------|--------|
| 階段一 | 1-2 天 | 後端交易完整性支援 | AddContactAndAssociateAsync API |
| 階段二 | 2-3 天 | 前端整合優化 | ContactContext、ContactManagementCenter |
| 階段三 | 1-2 天 | 用戶體驗優化 | 響應式設計、操作流程統一 |
| **總計** | **4-7 天** | **完整優化方案** | **統一的 Contact 管理體驗** |

## 成功指標

- **操作效率**：減少 50% 的操作步驟
- **資料一致性**：100% 交易完整性保證
- **用戶滿意度**：操作流程順暢直觀
- **維護成本**：統一狀態管理，減少重複代碼
