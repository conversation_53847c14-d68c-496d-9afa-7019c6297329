# IMS-Contact-004 任務狀態

## 當前狀態
**整體進度**：0% 完成
**當前階段**：任務整合完成，準備開始執行
**狀態**：📋 待辦（已整合 IMS-Contact-003 剩餘工作）
**最後更新**：2025-01-13 下午 6:00

## 任務整合說明

**🔄 重要更新**：本任務已整合 IMS-Contact-003 的剩餘工作項目，形成增強版重構任務：

**整合內容**：
- ✅ **IMS-Contact-003 階段一、二**：前後端驗證規則同步（已完成）
- 🔄 **IMS-Contact-003 階段三**：表單欄位優化 → 整合到階段一 ContactFormModal 重構
- 🔄 **IMS-Contact-003 階段四**：資料流轉優化 → 整合到階段一統一資料映射邏輯
- 🔄 **IMS-Contact-003 階段五**：測試與驗證 → 整合到階段三整合測試

**整合效益**：
- 避免 95% 重複工作
- 節省 25% 開發時間（2天）
- 同時達成兩個任務的所有目標

## 進度追蹤

### 📋 待辦項目

#### 階段一：組件標準化重構（預計 2-3 天）

##### 1.1 ContactFormModal 重構（預計 1.5 天）
- [ ] **引入 ResponsiveModalConfig**
  - [ ] 使用 `getResponsiveModalConfig(screenSize)` 函數
  - [ ] 統一響應式設計配置
  - [ ] 支援移動端、平板、桌面三種斷點

- [ ] **重構表單模式支援**
  - [ ] 實作 `create` 模式：完整新增（8個基本欄位）
  - [ ] 實作 `quick` 模式：快速新增（4個基本欄位 + 5個 PartnerContact 欄位）
  - [ ] 實作 `edit` 模式：編輯（根據情境顯示相應欄位）

- [ ] **統一欄位設計**
  - [ ] 基礎欄位：姓名*、電子郵件、聯絡人類型、狀態
  - [ ] 詳細欄位：職位、電話、部門、公司
  - [ ] 關聯欄位：角色*、優先序*、專業領域、備註、業務範圍

- [ ] **參考 ItemFormModal 設計模式**
  - [ ] 使用 Card 組件分組欄位
  - [ ] 統一的表單驗證邏輯
  - [ ] 一致的按鈕佈局和樣式

##### 1.2 篩選器統一重構（預計 1 天）
- [ ] **定義 contactFilterOptions**
  - [ ] 使用 CommonFilterOptions 模板
  - [ ] 支援姓名、電子郵件、公司、部門搜尋
  - [ ] 支援聯絡人類型、狀態篩選

- [ ] **實作 applyContactFilters 函數**
  - [ ] 參考 Item 模組的 applyItemFilters 設計
  - [ ] 支援搜尋文字篩選
  - [ ] 支援動態篩選條件

- [ ] **替換現有篩選組件**
  - [ ] 移除 ContactFilters.tsx
  - [ ] 移除 ContactSearchForm.tsx
  - [ ] 使用 FilterSearchContainer 統一管理

##### 1.3 ContactTable 優化（預計 0.5 天）
- [ ] **檢查 ResponsiveTable 使用**
  - [ ] 確認所有功能正常
  - [ ] 優化行動端卡片顯示
  - [ ] 統一排序和篩選功能

- [ ] **智能篩選器整合**
  - [ ] 使用 SmartColumnType 定義欄位
  - [ ] 啟用欄位級智能篩選器
  - [ ] 統一篩選器樣式

#### 階段二：操作流程優化（預計 1-2 天）

##### 2.1 Partner 頁面聯絡人管理優化（預計 1 天）
- [ ] **統一新增聯絡人入口**
  - [ ] Partner 頁面使用相同的 ContactFormModal
  - [ ] 傳入 `mode="quick"` 參數
  - [ ] 自動處理 PartnerContact 關聯

- [ ] **優化快速新增流程**
  - [ ] 保持快速性：預設顯示4個基本欄位
  - [ ] 增加完整性：提供展開選項顯示詳細欄位
  - [ ] 自動填入 PartnerContact 相關欄位

- [ ] **改善關聯管理**
  - [ ] 簡化 PartnerContact 的建立流程
  - [ ] 優化編輯和刪除操作
  - [ ] 統一關聯狀態顯示

##### 2.2 主頁面聯絡人管理優化（預計 1 天）
- [ ] **整合 FilterSearchContainer**
  - [ ] 替換現有的篩選和搜尋組件
  - [ ] 使用統一的篩選選項配置
  - [ ] 優化篩選結果顯示

- [ ] **優化新增聯絡人流程**
  - [ ] 使用重構後的 ContactFormModal
  - [ ] 傳入 `mode="create"` 參數
  - [ ] 統一成功和錯誤處理

#### 階段三：共享組件提取（預計 1 天）

##### 3.1 組件移至 shared 目錄（預計 0.5 天）
- [ ] **移動組件檔案**
  - [ ] ContactFormModal → src/app/ims/components/shared/ContactFormModal.tsx
  - [ ] ContactTable → src/app/ims/components/shared/ContactTable.tsx
  - [ ] contactUtils → src/app/ims/components/shared/contactUtils.ts

- [ ] **更新匯入路徑**
  - [ ] 更新所有使用這些組件的檔案
  - [ ] 確保匯入路徑正確
  - [ ] 檢查無遺漏的參考

##### 3.2 文檔更新（預計 0.5 天）
- [ ] **更新 USAGE_GUIDE.md**
  - [ ] 新增 Contact 組件使用範例
  - [ ] 說明三種表單模式的使用方式
  - [ ] 提供篩選器配置範例

- [ ] **建立 Contact 組件文檔**
  - [ ] ContactFormModal 使用說明
  - [ ] ContactTable 配置選項
  - [ ] 最佳實踐建議

## 依賴關係

### 前置條件
- ✅ IMS-Contact-003 現狀分析已完成
- ✅ 前後端驗證規則已同步
- ✅ 前後端數據格式已統一
- ✅ Item 模組設計模式已確立

### 外部依賴
- **IMS 共通組件**：FilterSearchContainer、ResponsiveTable、ResponsiveModalConfig
- **設計系統**：CommonFilterOptions、響應式斷點定義
- **現有組件**：ContactTable、ContactFormModal、contactUtils

## 風險與阻塞

### 當前風險
1. **現有功能中斷風險**（中等）
   - 緩解方案：分階段實施，保留備份組件
   - 狀態：已制定緩解計劃

2. **響應式設計問題**（低等）
   - 緩解方案：使用已驗證的 ResponsiveModalConfig
   - 狀態：風險可控

3. **使用者體驗變化**（低等）
   - 緩解方案：保持核心操作流程不變
   - 狀態：風險可控

### 當前阻塞
- 無阻塞項目

## 品質指標

### 目標指標
- **組件重用率**：提升至 90% 以上
- **代碼重複率**：降低至 10% 以下
- **響應式兼容性**：100% 支援三種斷點
- **TypeScript 覆蓋率**：100%

### 驗收標準
- [ ] 所有 Contact 相關功能正常運作
- [ ] 無 TypeScript 編譯錯誤
- [ ] 響應式設計在所有斷點正常
- [ ] 操作流程順暢直觀
- [ ] 文檔完整準確

## 時程規劃

| 階段 | 開始日期 | 結束日期 | 狀態 |
|------|----------|----------|------|
| 階段一：組件標準化重構 | 待定 | 待定 | 📋 待辦 |
| 階段二：操作流程優化 | 待定 | 待定 | 📋 待辦 |
| 階段三：共享組件提取 | 待定 | 待定 | 📋 待辦 |

**預計總工期**：6 個工作天

## 備註

### 重要提醒
- 本任務基於 IMS-Contact-003 的現狀分析結果
- 必須參考 Item 模組的設計模式和最佳實踐
- 確保與現有 IMS 共通組件的兼容性
- 保持向後兼容性，避免破壞現有功能

### 相關文檔
- [IMS-Contact-003 現狀分析](../IMS-Contact-003/)
- [Item 模組設計參考](../../src/app/ims/basic/item/)
- [IMS 共通組件使用指南](../../src/app/ims/components/shared/USAGE_GUIDE.md)
- [FastERP 開發規範](../../DEVELOPMENT_GUIDELINES.md)
