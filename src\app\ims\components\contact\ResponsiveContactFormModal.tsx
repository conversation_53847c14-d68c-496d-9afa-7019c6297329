"use client";

import React, { useState, useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  Select,
  Switch,
  Button,
  Space,
  Row,
  Col,
  Divider,
  Typography,
  message,
  Steps,
  Card,
  Tabs
} from 'antd';
import {
  UserOutlined,
  PhoneOutlined,
  MailOutlined,
  EnvironmentOutlined,
  InfoCircleOutlined,
  CheckOutlined
} from '@ant-design/icons';

import { Contact } from '@/services/ims/ContactService';
import { PartnerContact } from '@/services/ims/partner';
import { useResponsive } from '@/hooks/useResponsive';
import { logger, SYMBOLS, createContextLogger } from '@/utils/logger';

// 創建上下文日誌器
const responsiveFormLogger = createContextLogger({ module: 'ResponsiveContactFormModal' });

const { Text, Title } = Typography;
const { TextArea } = Input;
const { Option } = Select;
const { TabPane } = Tabs;

// 組件屬性介面
interface ResponsiveContactFormModalProps {
  visible: boolean;
  mode: 'create' | 'edit' | 'quick';
  contact?: Contact | null;
  partnerID?: string;
  onClose: () => void;
  onSubmit: (contact: Contact) => void;
  title?: string;
  className?: string;
}

// 表單模式類型
type FormMode = 'basic' | 'advanced' | 'partner';

const ResponsiveContactFormModal: React.FC<ResponsiveContactFormModalProps> = ({
  visible,
  mode,
  contact,
  partnerID,
  onClose,
  onSubmit,
  title,
  className = ''
}) => {
  // 響應式 Hook
  const { isMobile, isTablet, isDesktop } = useResponsive();
  
  // 表單實例
  const [form] = Form.useForm();
  
  // 本地狀態
  const [currentStep, setCurrentStep] = useState(0);
  const [formMode, setFormMode] = useState<FormMode>('basic');
  const [loading, setLoading] = useState(false);

  // 聯絡人類型選項
  const contactTypeOptions = [
    { value: 'internal', label: '內部員工' },
    { value: 'external', label: '外部聯絡人' },
    { value: 'customer', label: '客戶' },
    { value: 'supplier', label: '供應商' },
    { value: 'partner', label: '合作夥伴' }
  ];

  // 部門選項（可以從 API 獲取）
  const departmentOptions = [
    { value: 'sales', label: '業務部' },
    { value: 'marketing', label: '行銷部' },
    { value: 'finance', label: '財務部' },
    { value: 'hr', label: '人資部' },
    { value: 'it', label: '資訊部' },
    { value: 'operations', label: '營運部' }
  ];

  // 初始化表單
  useEffect(() => {
    if (visible) {
      if (contact) {
        form.setFieldsValue({
          name: contact.name,
          position: contact.position,
          email: contact.email,
          phone: contact.phone,
          isActive: contact.isActive,
          contactType: contact.contactType,
          department: contact.department,
          company: contact.company
        });
      } else {
        form.resetFields();
        form.setFieldsValue({
          isActive: true,
          contactType: 'external'
        });
      }
      setCurrentStep(0);
      setFormMode('basic');
    }
  }, [visible, contact, form]);

  // 處理表單提交
  const handleSubmit = async () => {
    try {
      setLoading(true);
      responsiveFormLogger.log(SYMBOLS.LOADING, '開始提交聯絡人表單', { mode, formMode });

      const values = await form.validateFields();
      
      const contactData: Contact = {
        contactID: contact?.contactID || '',
        name: values.name,
        position: values.position || '',
        email: values.email || '',
        phone: values.phone || '',
        isActive: values.isActive ?? true,
        contactType: values.contactType || 'external',
        department: values.department || '',
        company: values.company || '',
        createTime: contact?.createTime || Date.now(),
        createUserId: contact?.createUserId || null,
        updateTime: Date.now(),
        updateUserId: contact?.updateUserId || null,
        deleteTime: contact?.deleteTime || null,
        deleteUserId: contact?.deleteUserId || null,
        isDeleted: false
      };

      await onSubmit(contactData);
      
      responsiveFormLogger.log(SYMBOLS.SUCCESS, '聯絡人表單提交成功', { contactId: contactData.contactID });
      message.success(mode === 'edit' ? '聯絡人更新成功' : '聯絡人新增成功');
      
    } catch (error) {
      responsiveFormLogger.log(SYMBOLS.ERROR, '聯絡人表單提交失敗', error);
      if (error instanceof Error) {
        message.error('表單提交失敗：' + error.message);
      }
    } finally {
      setLoading(false);
    }
  };

  // 處理取消
  const handleCancel = () => {
    form.resetFields();
    setCurrentStep(0);
    setFormMode('basic');
    onClose();
  };

  // 步驟配置
  const steps = [
    {
      title: '基本資訊',
      description: '姓名、職位、公司'
    },
    {
      title: '聯絡方式',
      description: '電話、信箱'
    },
    ...(mode === 'quick' ? [{
      title: '關聯設定',
      description: '夥伴關聯資訊'
    }] : [])
  ];

  // 渲染基本資訊表單
  const renderBasicInfoForm = () => {
    return (
      <Card title="基本資訊" size="small">
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12}>
            <Form.Item
              name="name"
              label="姓名"
              rules={[
                { required: true, message: '請輸入姓名' },
                { min: 2, max: 50, message: '姓名長度應在 2-50 個字符之間' }
              ]}
            >
              <Input 
                prefix={<UserOutlined />} 
                placeholder="請輸入姓名"
                size={isMobile ? 'middle' : 'large'}
              />
            </Form.Item>
          </Col>
          <Col xs={24} sm={12}>
            <Form.Item
              name="position"
              label="職位"
              rules={[
                { max: 100, message: '職位長度不能超過 100 個字符' }
              ]}
            >
              <Input 
                placeholder="請輸入職位"
                size={isMobile ? 'middle' : 'large'}
              />
            </Form.Item>
          </Col>
          <Col xs={24} sm={12}>
            <Form.Item
              name="company"
              label="公司"
              rules={[
                { max: 100, message: '公司名稱長度不能超過 100 個字符' }
              ]}
            >
              <Input 
                placeholder="請輸入公司名稱"
                size={isMobile ? 'middle' : 'large'}
              />
            </Form.Item>
          </Col>
          <Col xs={24} sm={12}>
            <Form.Item
              name="department"
              label="部門"
            >
              <Select 
                placeholder="請選擇部門"
                allowClear
                size={isMobile ? 'middle' : 'large'}
              >
                {departmentOptions.map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col xs={24} sm={12}>
            <Form.Item
              name="contactType"
              label="聯絡人類型"
              rules={[{ required: true, message: '請選擇聯絡人類型' }]}
            >
              <Select 
                placeholder="請選擇聯絡人類型"
                size={isMobile ? 'middle' : 'large'}
              >
                {contactTypeOptions.map(option => (
                  <Option key={option.value} value={option.value}>
                    {option.label}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col xs={24} sm={12}>
            <Form.Item
              name="isActive"
              label="狀態"
              valuePropName="checked"
            >
              <Switch 
                checkedChildren="啟用" 
                unCheckedChildren="停用"
                size={isMobile ? 'default' : 'default'}
              />
            </Form.Item>
          </Col>
        </Row>
      </Card>
    );
  };

  // 渲染聯絡方式表單
  const renderContactInfoForm = () => {
    return (
      <Card title="聯絡方式" size="small">
        <Row gutter={[16, 16]}>
          <Col xs={24} sm={12}>
            <Form.Item
              name="email"
              label="電子信箱"
              rules={[
                { type: 'email', message: '請輸入有效的電子信箱' },
                { max: 100, message: '信箱長度不能超過 100 個字符' }
              ]}
            >
              <Input 
                prefix={<MailOutlined />} 
                placeholder="請輸入電子信箱"
                size={isMobile ? 'middle' : 'large'}
              />
            </Form.Item>
          </Col>
          <Col xs={24} sm={12}>
            <Form.Item
              name="phone"
              label="電話號碼"
              rules={[
                { max: 20, message: '電話號碼長度不能超過 20 個字符' }
              ]}
            >
              <Input 
                prefix={<PhoneOutlined />} 
                placeholder="請輸入電話號碼"
                size={isMobile ? 'middle' : 'large'}
              />
            </Form.Item>
          </Col>
        </Row>
      </Card>
    );
  };

  // 渲染夥伴關聯表單（快速模式）
  const renderPartnerAssociationForm = () => {
    if (mode !== 'quick' || !partnerID) return null;

    return (
      <Card title="夥伴關聯設定" size="small">
        <Row gutter={[16, 16]}>
          <Col xs={24}>
            <Form.Item
              name="role"
              label="角色"
              rules={[{ required: true, message: '請輸入角色' }]}
            >
              <Input 
                placeholder="請輸入在夥伴關係中的角色"
                size={isMobile ? 'middle' : 'large'}
              />
            </Form.Item>
          </Col>
          <Col xs={24} sm={12}>
            <Form.Item
              name="priority"
              label="優先級"
            >
              <Select 
                placeholder="請選擇優先級"
                size={isMobile ? 'middle' : 'large'}
              >
                <Option value="high">高</Option>
                <Option value="medium">中</Option>
                <Option value="low">低</Option>
              </Select>
            </Form.Item>
          </Col>
          <Col xs={24} sm={12}>
            <Form.Item
              name="expertise"
              label="專業領域"
            >
              <Input 
                placeholder="請輸入專業領域"
                size={isMobile ? 'middle' : 'large'}
              />
            </Form.Item>
          </Col>
          <Col xs={24}>
            <Form.Item
              name="notes"
              label="備註"
            >
              <TextArea 
                rows={3}
                placeholder="請輸入備註資訊"
                size={isMobile ? 'middle' : 'large'}
              />
            </Form.Item>
          </Col>
        </Row>
      </Card>
    );
  };

  // 渲染表單內容
  const renderFormContent = () => {
    if (isMobile) {
      // 移動端使用標籤頁
      return (
        <Tabs 
          activeKey={formMode} 
          onChange={(key) => setFormMode(key as FormMode)}
          size="small"
        >
          <TabPane tab="基本資訊" key="basic">
            {renderBasicInfoForm()}
          </TabPane>
          <TabPane tab="聯絡方式" key="advanced">
            {renderContactInfoForm()}
          </TabPane>
          {mode === 'quick' && (
            <TabPane tab="關聯設定" key="partner">
              {renderPartnerAssociationForm()}
            </TabPane>
          )}
        </Tabs>
      );
    } else {
      // 桌面端使用步驟
      return (
        <div>
          <Steps 
            current={currentStep} 
            items={steps}
            size="small"
            style={{ marginBottom: 24 }}
          />
          
          {currentStep === 0 && renderBasicInfoForm()}
          {currentStep === 1 && renderContactInfoForm()}
          {currentStep === 2 && mode === 'quick' && renderPartnerAssociationForm()}
        </div>
      );
    }
  };

  // 渲染操作按鈕
  const renderActionButtons = () => {
    if (isMobile) {
      return (
        <Space style={{ width: '100%', justifyContent: 'space-between' }}>
          <Button onClick={handleCancel} size="large">
            取消
          </Button>
          <Button 
            type="primary" 
            onClick={handleSubmit} 
            loading={loading}
            size="large"
            icon={<CheckOutlined />}
          >
            {mode === 'edit' ? '更新' : '新增'}
          </Button>
        </Space>
      );
    } else {
      return (
        <Space>
          <Button onClick={handleCancel}>
            取消
          </Button>
          {currentStep > 0 && (
            <Button onClick={() => setCurrentStep(currentStep - 1)}>
              上一步
            </Button>
          )}
          {currentStep < steps.length - 1 ? (
            <Button type="primary" onClick={() => setCurrentStep(currentStep + 1)}>
              下一步
            </Button>
          ) : (
            <Button 
              type="primary" 
              onClick={handleSubmit} 
              loading={loading}
              icon={<CheckOutlined />}
            >
              {mode === 'edit' ? '更新' : '新增'}
            </Button>
          )}
        </Space>
      );
    }
  };

  return (
    <Modal
      title={title || (mode === 'edit' ? '編輯聯絡人' : '新增聯絡人')}
      open={visible}
      onCancel={handleCancel}
      width={isMobile ? '95%' : isTablet ? '80%' : '60%'}
      style={{ top: isMobile ? 10 : 20 }}
      footer={renderActionButtons()}
      className={`responsive-contact-form-modal ${className}`}
      destroyOnClose
    >
      <Form
        form={form}
        layout="vertical"
        size={isMobile ? 'middle' : 'large'}
        scrollToFirstError
      >
        {renderFormContent()}
      </Form>
    </Modal>
  );
};

export default ResponsiveContactFormModal;
