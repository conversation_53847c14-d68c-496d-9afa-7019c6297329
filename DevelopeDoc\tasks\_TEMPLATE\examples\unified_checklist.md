# 統一檢查清單

## 📋 開發前檢查

### 架構理解
- [ ] 已閱讀 `DEVELOPMENT_GUIDELINES.md`
- [ ] 了解 `ModuleRouteConvention.cs` 的路由處理機制
- [ ] 檢查了前端 `api.ts` 的路由配置
- [ ] 確認前後端路由一致性

### 需求分析
- [ ] 已分析現有功能和需求
- [ ] 已確認後端 API 實作狀況
- [ ] 已分析使用者使用情境
- [ ] 已確認資料庫結構
- [ ] 已分析權限需求

## 📋 開發中檢查

### 程式碼品質
- [ ] 遵循變數命名規範
- [ ] 使用標準的 CRUD 方法命名
- [ ] 遵循 API 設計原則
- [ ] 正確處理 Transaction
- [ ] 遵循權限檢查規範

### 架構一致性
- [ ] 前後端介面屬性一致性
- [ ] API 端點命名一致性
- [ ] 資料驗證邏輯同步
- [ ] 服務層函數命名對應

## 📋 開發後檢查

### 品質保證
- [ ] 通過所有 linter 檢查
- [ ] 通過前後端整合測試
- [ ] 確保資料一致性
- [ ] 更新相關文件

### 最終驗收
- [ ] 功能符合需求規格
- [ ] 效能滿足要求
- [ ] 安全性檢查通過
- [ ] 使用者體驗良好

## 🔗 相關文件

- **架構參考**：`architecture_reference.md`
- **一致性檢查**：`architecture_consistency_checklist.md`
- **開發準則**：`../../DEVELOPMENT_GUIDELINES.md`
