# IMS-Contact-003 實作規劃

## 任務概述
聯絡人表單驗證規則與欄位同步，解決快速新增與完整新增聯絡人的不一致問題，並同步前後端驗證規則。

## 階段規劃

### 階段一：後端驗證規則分析與整理（預計 0.5 天）

#### 1.1 後端模型驗證分析
- [ ] **分析 Contact 模型驗證屬性**
  - 檢查 `Contact.cs` 中的 `[Required]` 屬性
  - 檢查 `ContactDTO.cs` 中的驗證屬性
  - 整理必填欄位清單

- [ ] **分析 PartnerContact 模型驗證屬性**
  - 檢查 `PartnerContact.cs` 中的驗證屬性
  - 檢查 `PartnerContactDTO.cs` 中的驗證屬性
  - 確認預設值設定（Priority = 99）

- [ ] **業務規則確認**
  - 確認哪些欄位在業務邏輯上應該為必填
  - 確認預設值的合理性
  - 與產品需求對齊

#### 1.2 驗證規則文件化
- [ ] **建立驗證規則對照表**
  - Contact 模型必填欄位
  - PartnerContact 模型必填欄位
  - 預設值設定
  - 最大長度限制

### 階段二：前端驗證規則同步（預計 1 天）

#### 2.1 更新共享驗證規則
- [ ] **更新 contactValidation.ts**
  - 移除不合理的必填限制（優先序、角色）
  - 只保留後端標記為 `[Required]` 的欄位為必填
  - 統一錯誤訊息格式

- [ ] **更新 contactConstants.ts**
  - 調整預設值設定
  - 確保與後端預設值一致
  - 更新驗證規則常數

#### 2.2 修復現有組件
- [ ] **修復 ContactManagementTab**
  - 移除優先序的必填驗證
  - 移除角色的必填驗證
  - 設定適當的預設值

- [ ] **驗證規則測試**
  - 更新 `contactValidation.test.ts`
  - 確保測試覆蓋新的驗證規則
  - 驗證前後端一致性

### 階段三：表單欄位優化（預計 1 天）

#### 3.1 快速模式欄位擴充
- [ ] **分析欄位需求**
  - 確認快速模式應包含的欄位
  - 平衡簡潔性與完整性
  - 設計更好的佈局

- [ ] **更新 ContactFormFields**
  - 調整快速模式欄位配置
  - 增加職位、電話欄位到快速模式
  - 優化欄位佈局和分組

#### 3.2 表單佈局優化
- [ ] **響應式佈局改進**
  - 優化移動端顯示
  - 改進欄位分組
  - 提升填寫體驗

- [ ] **預設值和提示優化**
  - 統一所有表單的預設值
  - 改進欄位提示和說明
  - 優化必填欄位標示

### 階段四：資料流轉優化（預計 0.5 天）

#### 4.1 資料帶入邏輯優化
- [ ] **快速新增資料流轉**
  - 確保快速新增的資料能完整帶入編輯表單
  - 優化預設值處理邏輯
  - 處理欄位映射問題

- [ ] **表單初始化優化**
  - 統一表單初始化邏輯
  - 確保預設值正確設定
  - 優化資料綁定

### 階段五：測試與驗證（預計 0.5 天）

#### 5.1 功能測試
- [ ] **表單驗證測試**
  - 測試所有必填欄位驗證
  - 測試預設值設定
  - 測試錯誤訊息顯示

- [ ] **資料流轉測試**
  - 測試快速新增到編輯的資料流轉
  - 測試不同模式間的資料一致性
  - 測試預設值處理

#### 5.2 回歸測試
- [ ] **現有功能測試**
  - 確保修改不影響現有功能
  - 測試所有聯絡人相關操作
  - 驗證前後端整合

## 里程碑

| 里程碑 | 預計完成日期 | 驗收條件 | 依賴關係 |
|--------|-------------|----------|----------|
| M1 後端驗證分析完成 | Day 1 | 完成驗證規則對照表 | - |
| M2 前端驗證同步完成 | Day 2 | 前後端驗證規則一致 | M1 |
| M3 表單欄位優化完成 | Day 3 | 快速模式包含必要欄位 | M2 |
| M4 資料流轉優化完成 | Day 3.5 | 資料在表單間無縫流轉 | M3 |
| M5 測試驗證完成 | Day 4 | 所有測試通過 | M4 |

## 技術實作細節

### 關鍵修改點

#### contactValidation.ts
```typescript
// 移除不合理的必填限制
export const getContactFieldValidationRules = (fieldName: string): ValidationRule[] => {
  switch (fieldName) {
    case 'name':
      return getNameValidationRules(); // 保持必填
    case 'priority':
      return getPriorityValidationRules(); // 移除必填，只保留範圍驗證
    case 'role':
      return getRoleValidationRules(); // 移除必填，只保留長度驗證
    // ... 其他欄位
  }
};
```

#### contactConstants.ts
```typescript
// 調整快速模式欄位
export const CONTACT_FORM_FIELDS = {
  quick: ["name", "position", "email", "phone", "contactType", "status"] as const, // 增加 position, phone
  all: ["name", "position", "email", "phone", "department", "company", "contactType", "status"] as const
} as const;

// 統一預設值
export const CONTACT_DEFAULTS = {
  status: true,
  contactType: "客戶",
  priority: 99, // 與後端一致
  role: "" // 空字串，允許用戶選填
} as const;
```

#### ContactManagementTab.tsx
```typescript
// 移除必填驗證
<Form.Item
  label="優先序"
  name="priority"
  // rules={[{ required: true, message: '請輸入優先序' }]} // 移除此行
  tooltip="數字越小優先級越高，0 代表主要聯絡人"
>
  <InputNumber 
    min={0} 
    max={999} 
    style={{ width: '100%' }}
    placeholder="預設 99"
    defaultValue={99} // 設定預設值
  />
</Form.Item>
```

## 風險控制

### 主要風險
1. **修改驗證規則影響現有功能**
   - 緩解：充分的回歸測試
   - 應變：準備回滾方案

2. **用戶體驗改變**
   - 緩解：提供清晰的欄位說明
   - 應變：收集用戶反饋並快速調整

### 測試策略
- 單元測試：驗證規則函數
- 整合測試：表單提交流程
- 端對端測試：完整的用戶操作流程
- 回歸測試：確保現有功能不受影響

## 驗收檢查清單

### 功能驗收
- [ ] 只有「姓名」為必填欄位
- [ ] 優先序預設值為 99，非必填
- [ ] 角色為選填欄位
- [ ] 快速模式包含 6 個欄位（姓名、職位、電子郵件、電話、聯絡人類型、狀態）
- [ ] 完整模式包含 8 個欄位
- [ ] 前後端驗證規則完全一致

### 技術驗收
- [ ] 所有 TypeScript 編譯通過
- [ ] 所有 ESLint 檢查通過
- [ ] 所有單元測試通過
- [ ] 所有整合測試通過

### 用戶體驗驗收
- [ ] 表單填寫流程順暢
- [ ] 錯誤訊息清晰準確
- [ ] 快速新增操作保持簡潔
- [ ] 資料流轉無遺失

## 後續改進建議

1. **動態驗證規則**：考慮根據不同場景動態調整驗證規則
2. **欄位依賴關係**：實現欄位間的依賴驗證
3. **批量操作優化**：針對批量新增聯絡人的場景進行優化
4. **國際化支援**：為驗證訊息添加多語言支援
