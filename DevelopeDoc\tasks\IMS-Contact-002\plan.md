# IMS-Contact-002 實作規劃

## 總體規劃

### 專案範圍
- **主要目標**：統一聯絡人管理架構，消除重複邏輯，提升維護性
- **實作方式**：前端重構，充分利用現有系統級工具和共享組件
- **預估工期**：5-7 個工作日
- **風險等級**：中等（影響範圍大但邏輯相對簡單）

### 核心策略
1. **充分利用現有基礎設施**：`@Tools/`、`@AdvancedFilterComponent`、`@ResponsiveTable` 等
2. **漸進式重構**：分階段實作，確保每階段都能正常運作
3. **向後相容**：不改變現有 API 和使用者操作流程
4. **測試驅動**：每個階段都有完整的功能測試

## 階段劃分

### 階段一：建立共享基礎設施（1-2 天）

#### 里程碑 1.1：建立常數和工具層
**目標**：建立統一的常數定義和工具函數

**任務清單**：
- [x] 建立 `contactConstants.ts`：統一所有選項和常數定義
- [x] 建立 `contactValidation.ts`：統一驗證規則（對應後端 ValidationHelper）
- [x] 建立 `contactUtils.ts`：統一工具函數（正規化、搜尋、篩選等）
- [ ] 單元測試：確保工具函數正確性
- [ ] 型別檢查：確保 TypeScript 編譯無誤

**驗收條件**：
- [ ] 所有常數定義完整且與現有選項一致
- [ ] 驗證規則與後端 `ValidationHelper.cs` 保持一致
- [ ] 工具函數通過單元測試
- [ ] 無 TypeScript 編譯錯誤

**預期產出**：
```
src/app/ims/components/contact/shared/
├── contactConstants.ts     ✅ 已完成
├── contactValidation.ts    ✅ 已完成  
├── contactUtils.ts         ✅ 已完成
└── __tests__/             ⏳ 待實作
    ├── contactValidation.test.ts
    └── contactUtils.test.ts
```

#### 里程碑 1.2：建立共享表單組件
**目標**：建立可重用的表單欄位組件

**任務清單**：
- [ ] 建立 `ContactFormFields.tsx`：純表單欄位組件
- [ ] 支援不同模式：`quick`（快速）、`full`（完整）、`readonly`（唯讀）
- [ ] 整合 `contactConstants` 和 `contactValidation`
- [ ] 響應式設計支援

**驗收條件**：
- [ ] 支援所有聯絡人欄位的渲染
- [ ] 驗證規則正確執行
- [ ] 在不同螢幕尺寸下正常顯示
- [ ] 支援受控和非受控模式

**預期產出**：
```typescript
interface ContactFormFieldsProps {
  mode?: 'quick' | 'full' | 'readonly';
  initialValues?: Partial<Contact>;
  onChange?: (values: Partial<Contact>) => void;
  isMobile?: boolean;
}
```

### 階段二：重構現有組件（2-3 天）

#### 里程碑 2.1：重構 ContactFormModal
**目標**：使用共享組件重構聯絡人表單模態框

**任務清單**：
- [ ] 替換內部表單邏輯為 `ContactFormFields`
- [ ] 使用 `@ResponsiveModalConfig` 優化響應式行為
- [ ] 整合統一的驗證和預設值
- [ ] 確保 API 呼叫使用統一的 `normalizeContact`

**驗收條件**：
- [ ] 功能與重構前完全一致
- [ ] 程式碼行數減少 50% 以上
- [ ] 在各種螢幕尺寸下正常運作

#### 里程碑 2.2：重構 ContactListModal
**目標**：使用 `@ResponsiveTable` 和 `@AdvancedFilterComponent` 重構聯絡人清單

**任務清單**：
- [ ] 替換內部表格為 `ResponsiveTable`
- [ ] 替換篩選邏輯為 `AdvancedFilterComponent`
- [ ] 使用 `CONTACT_FILTER_OPTIONS` 配置篩選器
- [ ] 整合統一的搜尋邏輯

**驗收條件**：
- [ ] 篩選功能與重構前一致
- [ ] 支援智能篩選器（依資料量自動調整）
- [ ] 移動端自動切換為卡片模式
- [ ] 搜尋響應時間不超過 300ms

#### 里程碑 2.3：重構 ContactManagementTab
**目標**：重構夥伴聯絡人管理標籤頁

**任務清單**：
- [ ] 快速新增表單使用 `ContactFormFields`（quick 模式）
- [ ] 整合統一的 `resolveCreatedContact` 後援機制
- [ ] 使用統一的 `normalizePartnerContacts` 邏輯
- [ ] 表格使用 `ResponsiveTable` 的拖拽排序功能

**驗收條件**：
- [ ] 快速新增與完整新增功能完全一致
- [ ] 後援查找機制正常運作
- [ ] 拖拽排序功能保持正常
- [ ] 資料正規化邏輯統一

### 階段三：整合測試與優化（1-2 天）

#### 里程碑 3.1：整合測試
**目標**：確保所有聯絡人功能正常運作

**任務清單**：
- [ ] 端到端測試：完整的聯絡人管理流程
- [ ] 回歸測試：確保現有功能不受影響
- [ ] 效能測試：確保重構後效能不降低
- [ ] 響應式測試：各種螢幕尺寸下的功能測試

**驗收條件**：
- [ ] 所有現有功能正常運作
- [ ] 新舊功能行為完全一致
- [ ] 效能指標符合要求
- [ ] 無 console 錯誤或警告

#### 里程碑 3.2：程式碼品質優化
**目標**：確保程式碼品質和可維護性

**任務清單**：
- [ ] 移除重複的程式碼和未使用的匯入
- [ ] 優化 TypeScript 型別定義
- [ ] 新增必要的註解和文件
- [ ] 通過 ESLint 和 Prettier 檢查

**驗收條件**：
- [ ] 程式碼重複度降低 80% 以上
- [ ] TypeScript 嚴格模式無錯誤
- [ ] 所有公共函數都有 JSDoc 註解
- [ ] 通過程式碼品質檢查

## 詳細任務分解

### 高優先級任務（必須完成）
1. **建立共享基礎設施**：常數、驗證、工具函數
2. **重構核心組件**：ContactFormModal、ContactListModal、ContactManagementTab
3. **整合測試**：確保功能正確性

### 中優先級任務（重要但可延後）
1. **效能優化**：大資料量處理、搜尋優化
2. **使用者體驗優化**：載入狀態、錯誤提示
3. **程式碼文件**：README、API 文件

### 低優先級任務（可選）
1. **單元測試覆蓋**：達到 80% 以上覆蓋率
2. **國際化準備**：提取可翻譯字串
3. **無障礙功能**：鍵盤導航、螢幕閱讀器支援

## 風險分析與緩解

### 高風險項目

#### 風險 1：重構影響現有功能
**風險等級**：高  
**影響**：使用者無法正常使用聯絡人功能  
**機率**：中等  

**緩解措施**：
- 分階段實作，每階段都有完整測試
- 保留舊版本作為備援
- 詳細的回歸測試計畫
- 快速回滾機制

#### 風險 2：效能下降
**風險等級**：中等  
**影響**：使用者體驗變差  
**機率**：低  

**緩解措施**：
- 效能基準測試
- 程式碼分割和懶載入
- 快取機制優化
- 效能監控

### 中風險項目

#### 風險 3：時程延誤
**風險等級**：中等  
**影響**：專案交付延後  
**機率**：中等  

**緩解措施**：
- 保守的時程估算
- 每日進度檢查
- 及時調整任務優先級
- 必要時縮小範圍

## 驗收標準

### 功能驗收
- [ ] 所有聯絡人 CRUD 功能正常
- [ ] 快速新增與完整新增行為一致
- [ ] 搜尋篩選功能正常
- [ ] 夥伴聯絡人關聯管理正常
- [ ] 響應式設計在各裝置上正常

### 技術驗收
- [ ] 程式碼重複度降低 80% 以上
- [ ] TypeScript 編譯無錯誤和警告
- [ ] ESLint 和 Prettier 檢查通過
- [ ] 單元測試覆蓋率達到 70% 以上

### 效能驗收
- [ ] 頁面載入時間 < 2 秒
- [ ] 搜尋響應時間 < 300ms
- [ ] 支援 1000+ 筆資料流暢操作
- [ ] 記憶體使用量無明顯增加

### 使用者體驗驗收
- [ ] 操作流程與重構前一致
- [ ] 錯誤提示清楚易懂
- [ ] 載入狀態提示適當
- [ ] 移動端操作流暢

## 部署計畫

### 部署策略
1. **開發環境**：完整功能測試
2. **測試環境**：使用者驗收測試
3. **預生產環境**：效能和壓力測試
4. **生產環境**：分批部署，監控指標

### 回滾計畫
- 保留舊版本程式碼
- 快速回滾腳本
- 資料庫變更回滾（如有）
- 監控指標異常自動回滾

### 監控指標
- 頁面載入時間
- API 回應時間
- 錯誤率
- 使用者操作成功率

## 後續維護

### 文件更新
- [ ] 更新開發者文件
- [ ] 更新使用者手冊（如有）
- [ ] 更新 API 文件
- [ ] 建立故障排除指南

### 知識傳承
- [ ] 團隊技術分享
- [ ] 程式碼審查要點
- [ ] 最佳實踐文件
- [ ] 常見問題解答

### 未來改進
- [ ] 效能持續優化
- [ ] 使用者體驗改進
- [ ] 新功能擴展準備
- [ ] 技術債務清理
