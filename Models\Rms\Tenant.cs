using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using Microsoft.EntityFrameworkCore;
using System.Text.Json.Serialization;
using FAST_ERP_Backend.Attributes;

namespace FAST_ERP_Backend.Models.Rms
{
    /// <summary>
    /// 租戶資料表
    /// </summary>
    public class Tenant : ModelBaseEntity
    {
        [Key]
        [Comment("租戶編號")]
        [Column(TypeName = "nvarchar(100)")]
        public string TenantId { get; set; } // 租戶編號

        [Comment("租戶名稱")]
        [Column(TypeName = "nvarchar(100)")]
        public string TenantName { get; set; } // 租戶名稱

        [Comment("聯絡人")]
        [Column(TypeName = "nvarchar(100)")]
        public string ContactPerson { get; set; } // 聯絡人

        [Comment("聯絡人身分證編號")]
        [Column(TypeName = "nvarchar(10)")]
        public string ContactIdNumber { get; set; } // 聯絡人身分證編號

        [Comment("聯絡電話")]
        [Column(TypeName = "nvarchar(50)")]
        public string ContactPhone { get; set; } // 聯絡電話

        [Comment("電子郵件")]
        [Column(TypeName = "nvarchar(100)")]
        public string Email { get; set; } // 電子郵件

        [Comment("通訊地址")]
        [Column(TypeName = "nvarchar(200)")]
        public string Address { get; set; } // 通訊地址

        [Comment("備註")]
        [Column(TypeName = "nvarchar(MAX)")]
        public string Note { get; set; } // 備註

        // Navigation Properties
        public virtual ICollection<Contract> Contracts { get; set; }

        public Tenant()
        {
            Contracts = new HashSet<Contract>();
        }
    }

    /// <summary>
    /// 租戶資料傳輸物件
    /// </summary>
    public class TenantDTO
    {
        public string TenantId { get; set; } // 租戶編號
        public string TenantName { get; set; } // 租戶名稱
        public string ContactPerson { get; set; } // 聯絡人
        public string ContactIdNumber { get; set; } // 聯絡人身分證編號
        public string ContactPhone { get; set; } // 聯絡電話
        public string Email { get; set; } // 電子郵件
        public string Address { get; set; } // 通訊地址
        public string Note { get; set; } // 備註
        public long CreateTime { get; set; } // 建立時間
        public string CreateUserId { get; set; } // 建立者ID

        public TenantDTO()
        {
        }
    }

    /// <summary>
    /// 租戶查詢請求 DTO
    /// </summary>
    public class TenantQueryRequestDTO
    {
        public string? TenantId { get; set; }
        public string? TenantName { get; set; }
        public string? ContactPerson { get; set; }
        public string? ContactIdNumber { get; set; }
        public string? ContactPhone { get; set; }
        public string? Email { get; set; }
    }

    /// <summary>
    /// 租戶新增請求 DTO
    /// </summary>
    public class TenantCreateRequestDTO
    {
        [Required(ErrorMessage = "租戶名稱不能為空")]
        [StringLength(100, MinimumLength = 2, ErrorMessage = "租戶名稱長度必須在2-100字元之間")]
        [RegularExpression(@"^[\u4e00-\u9fa5a-zA-Z0-9_\s]+$", ErrorMessage = "租戶名稱只能包含中文、英文、數字、底線和空格")]
        [TenantCreateValidation]
        public string TenantName { get; set; }

        [Required(ErrorMessage = "聯絡人不能為空")]
        [StringLength(100, MinimumLength = 2, ErrorMessage = "聯絡人長度必須在2-100字元之間")]
        [RegularExpression(@"^[\u4e00-\u9fa5a-zA-Z\s]+$", ErrorMessage = "聯絡人只能包含中文、英文和空格")]
        public string ContactPerson { get; set; }

        [Required(ErrorMessage = "聯絡人身分證編號不能為空")]
        [UniformNumber]
        public string ContactIdNumber { get; set; }

        [Required(ErrorMessage = "聯絡電話不能為空")]
        [PhoneNumber]
        public string ContactPhone { get; set; }

        [Email]
        public string Email { get; set; }

        [StringLength(200, ErrorMessage = "通訊地址長度不能超過200字元")]
        public string Address { get; set; }

        [StringLength(500, ErrorMessage = "備註長度不能超過500字元")]
        public string Note { get; set; }
    }

    /// <summary>
    /// 租戶更新請求 DTO
    /// </summary>
    public class TenantUpdateRequestDTO
    {
        [Required(ErrorMessage = "租戶編號不能為空")]
        [TenantUpdateValidation]
        public string TenantId { get; set; }

        [Required(ErrorMessage = "租戶名稱不能為空")]
        [StringLength(100, MinimumLength = 2, ErrorMessage = "租戶名稱長度必須在2-100字元之間")]
        [RegularExpression(@"^[\u4e00-\u9fa5a-zA-Z0-9_\s]+$", ErrorMessage = "租戶名稱只能包含中文、英文、數字、底線和空格")]
        public string TenantName { get; set; }

        [Required(ErrorMessage = "聯絡人不能為空")]
        [StringLength(100, MinimumLength = 2, ErrorMessage = "聯絡人長度必須在2-100字元之間")]
        [RegularExpression(@"^[\u4e00-\u9fa5a-zA-Z\s]+$", ErrorMessage = "聯絡人只能包含中文、英文和空格")]
        public string ContactPerson { get; set; }

        [Required(ErrorMessage = "聯絡人身分證編號不能為空")]
        [UniformNumber]
        public string ContactIdNumber { get; set; }

        [Required(ErrorMessage = "聯絡電話不能為空")]
        [PhoneNumber]
        public string ContactPhone { get; set; }

        [Email]
        public string Email { get; set; }

        [StringLength(200, ErrorMessage = "通訊地址長度不能超過200字元")]
        public string Address { get; set; }

        [StringLength(500, ErrorMessage = "備註長度不能超過500字元")]
        public string Note { get; set; }
    }

    /// <summary>
    /// 租戶刪除請求 DTO
    /// </summary>
    public class TenantDeleteRequestDTO
    {
        [Required(ErrorMessage = "租戶編號不能為空")]
        [TenantExists]
        [TenantCanDelete]
        public string TenantId { get; set; }
    }

    /// <summary>
    /// 租戶可刪除驗證屬性（檢查是否有相關合約）
    /// </summary>
    public class TenantCanDeleteAttribute : ValidationAttribute
    {
        public TenantCanDeleteAttribute()
        {
            ErrorMessage = "該租戶有相關合約，無法刪除";
        }

        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            if (value == null || string.IsNullOrWhiteSpace(value.ToString()))
            {
                return ValidationResult.Success; // 空值由其他驗證處理
            }

            // 嘗試從服務容器中取得 DbContext
            var context = validationContext.GetService(typeof(ERPDbContext)) as ERPDbContext;
            if (context == null)
            {
                return new ValidationResult("無法取得資料庫連線，請聯繫系統管理員");
            }

            var tenantId = value.ToString();
            
            // 檢查是否有相關合約（全域查詢過濾器會自動過濾軟刪除的合約）
            var hasContracts = context.Rms_Contracts
                .Any(c => c.TenantId == tenantId);

            if (hasContracts)
            {
                return new ValidationResult(ErrorMessage);
            }

            return ValidationResult.Success;
        }
    }

    /// <summary>
    /// 租戶存在驗證屬性
    /// </summary>
    public class TenantExistsAttribute : ValidationAttribute
    {
        public TenantExistsAttribute()
        {
            ErrorMessage = "租戶不存在";
        }

        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            // 這裡只做基本的 null/empty 檢查
            // 實際的資料庫檢查會在 Service 層處理
            if (value == null || string.IsNullOrWhiteSpace(value.ToString()))
            {
                return new ValidationResult("租戶編號不能為空");
            }

            // 嘗試從服務容器中取得 DbContext
            var context = validationContext.GetService(typeof(ERPDbContext)) as ERPDbContext;
            if (context == null)
            {
                return new ValidationResult("無法取得資料庫連線，請聯繫系統管理員");
            }

            var tenantId = value.ToString();
            
            // 檢查租戶是否存在（全域查詢過濾器會自動過濾軟刪除的租戶）
            var exists = context.Rms_Tenants
                .Any(t => t.TenantId == tenantId);

            if (!exists)
            {
                return new ValidationResult(ErrorMessage);
            }

            return ValidationResult.Success;
        }
    }

    /// <summary>
    /// 租戶建立複合驗證屬性（優化 SQL 查詢）
    /// </summary>
    public class TenantCreateValidationAttribute : ValidationAttribute
    {
        public TenantCreateValidationAttribute()
        {
            ErrorMessage = "租戶建立驗證失敗";
        }

        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            if (value == null || string.IsNullOrWhiteSpace(value.ToString()))
            {
                return ValidationResult.Success; // 空值由其他驗證處理
            }

            // 嘗試從服務容器中取得 DbContext
            var context = validationContext.GetService(typeof(ERPDbContext)) as ERPDbContext;
            if (context == null)
            {
                return new ValidationResult("無法取得資料庫連線，請聯繫系統管理員");
            }

            // 獲取當前請求的 DTO 實例以進行額外的業務邏輯驗證
            var instance = validationContext.ObjectInstance;
            if (instance is TenantCreateRequestDTO createRequest)
            {
                // 檢查租戶名稱是否重複
                var existingTenant = context.Rms_Tenants
                    .FirstOrDefault(t => t.TenantName == createRequest.TenantName);

                if (existingTenant != null)
                {
                    return new ValidationResult("租戶名稱已存在");
                }

                // 檢查聯絡人身分證編號是否重複
                var existingIdNumber = context.Rms_Tenants
                    .FirstOrDefault(t => t.ContactIdNumber == createRequest.ContactIdNumber);

                if (existingIdNumber != null)
                {
                    return new ValidationResult("聯絡人身分證編號已存在");
                }

                // 檢查聯絡電話是否重複
                var existingPhone = context.Rms_Tenants
                    .FirstOrDefault(t => t.ContactPhone == createRequest.ContactPhone);

                if (existingPhone != null)
                {
                    return new ValidationResult("聯絡電話已存在");
                }
            }

            return ValidationResult.Success;
        }
    }

    /// <summary>
    /// 租戶更新複合驗證屬性（優化 SQL 查詢）
    /// </summary>
    public class TenantUpdateValidationAttribute : ValidationAttribute
    {
        public TenantUpdateValidationAttribute()
        {
            ErrorMessage = "租戶更新驗證失敗";
        }

        protected override ValidationResult IsValid(object value, ValidationContext validationContext)
        {
            if (value == null || string.IsNullOrWhiteSpace(value.ToString()))
            {
                return ValidationResult.Success; // 空值由其他驗證處理
            }

            // 嘗試從服務容器中取得 DbContext
            var context = validationContext.GetService(typeof(ERPDbContext)) as ERPDbContext;
            if (context == null)
            {
                return new ValidationResult("無法取得資料庫連線，請聯繫系統管理員");
            }

            var tenantId = value.ToString();

            // 一次查詢取得租戶資料
            var tenant = context.Rms_Tenants
                .FirstOrDefault(t => t.TenantId == tenantId);

            if (tenant == null)
            {
                return new ValidationResult("租戶不存在");
            }

            // 獲取當前請求的 DTO 實例以進行額外的業務邏輯驗證
            var instance = validationContext.ObjectInstance;
            if (instance is TenantUpdateRequestDTO updateRequest)
            {
                // 檢查租戶名稱是否重複（排除自己）
                var existingTenant = context.Rms_Tenants
                    .FirstOrDefault(t => t.TenantName == updateRequest.TenantName && t.TenantId != tenantId);

                if (existingTenant != null)
                {
                    return new ValidationResult("租戶名稱已存在");
                }

                // 檢查聯絡人身分證編號是否重複（排除自己）
                var existingIdNumber = context.Rms_Tenants
                    .FirstOrDefault(t => t.ContactIdNumber == updateRequest.ContactIdNumber && t.TenantId != tenantId);

                if (existingIdNumber != null)
                {
                    return new ValidationResult("聯絡人身分證編號已存在");
                }

                // 檢查聯絡電話是否重複（排除自己）
                var existingPhone = context.Rms_Tenants
                    .FirstOrDefault(t => t.ContactPhone == updateRequest.ContactPhone && t.TenantId != tenantId);

                if (existingPhone != null)
                {
                    return new ValidationResult("聯絡電話已存在");
                }
            }

            return ValidationResult.Success;
        }
    }
} 