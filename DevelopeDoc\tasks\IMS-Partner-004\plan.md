# 實作規劃（IMS-Partner-004）

## 目標與範圍
- 目標：完善新增/編輯商業夥伴時的「聯絡人管理」體驗與資料一致性
- 範圍：前端（PartnerFormModal、ContactManagementTab）、文件、最小必要服務驗證

## 開發流程檢查清單

### 階段一：需求與現況確認（完成）
- [x] 盤點現況與缺口（單一主要、Priority、去重、提交流程一致性）
- [x] 確認不需新增後端 API（沿用現有 CRUD）

### 階段二：規格設計（完成）
- [x] 規範 normalize 規則：
  - 去重以 contactID 為鍵
  - 單一主要（若多筆取 Priority 最小者，若無主要指定 Priority 最小者）
  - Priority：主要=0，其他無指定=99
  - 僅提交 DTO 必要欄位

### 階段三：實作（進行中）
- [x] 在 `PartnerFormModal.tsx` 於提交時套用 normalize
- [ ] 檢整 `ContactManagementTab.tsx` 型別一致與本地狀態行為
- [ ] 驗證「快速新增聯絡人」之本地關聯會納入提交

### 階段四：驗收
- [ ] 手測：新增/編輯/刪除/排序/設定主要/提交
- [ ] Type/Lint 檢查無誤

## 里程碑
- M1 規格確認與提交正規化完成（1 人日）
- M2 Tab 行為與快速新增一致性確認（1 人日）
- M3 驗收與文件更新（0.5 人日）

## 風險與緩解
- 風險：前端與 DTO 欄位差異 → 已建立 normalize 防呆
- 風險：本地清單與提交資料脫鉤 → 提交前統一來源於 Modal state
