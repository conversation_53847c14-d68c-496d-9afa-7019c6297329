// API 基礎路徑
//export const BASE_URL = "http://localhost:5015/";
export const BASE_URL = "https://localhost:7137/";
//export const BASE_URL = "http://localhost:8686/";
export const API_BASE_URL = `${BASE_URL}api`;

export const MODULE_COMMON_URL = "common";
export const MODULE_PAS_URL = "pas";
export const MODULE_PMS_URL = "pms";
export const MODULE_IMS_URL = "ims";
export const MODULE_SMS_URL = "sms";

// API 端點配置
export const apiEndpoints = {
    /**
     * Common 通用
     * **/
    // Login 登入管理
    login: `${API_BASE_URL}/${MODULE_COMMON_URL}/Login/VerifyLogin`,
    // Users 使用者資料管理
    getUsers: `${API_BASE_URL}/${MODULE_COMMON_URL}/Users/<USER>
    getMyInfo: `${API_BASE_URL}/${MODULE_COMMON_URL}/Users/<USER>
    getUserDetail: `${API_BASE_URL}/${MODULE_COMMON_URL}/Users/<USER>
    addUser: `${API_BASE_URL}/${MODULE_COMMON_URL}/Users/<USER>
    editUsers: `${API_BASE_URL}/${MODULE_COMMON_URL}/Users/<USER>
    deleteUser: `${API_BASE_URL}/${MODULE_COMMON_URL}/Users/<USER>
    changePassword: `${API_BASE_URL}/${MODULE_COMMON_URL}/Users/<USER>

    // RSA 加密管理
    getRsaPublicKey: `${API_BASE_URL}/${MODULE_COMMON_URL}/Rsa/GetPublicKey`,

    // AuditLogs 審核日誌管理
    getAuditLogs: `${API_BASE_URL}/${MODULE_COMMON_URL}/AuditLogs/GetAuditLogs`,
    getAuditLogDetail: `${API_BASE_URL}/${MODULE_COMMON_URL}/AuditLogs/GetAuditLogs`,
    addAuditLog: `${API_BASE_URL}/${MODULE_COMMON_URL}/AuditLogs/AddAuditLogs`,

    // City 縣市資料管理
    getCities: `${API_BASE_URL}/${MODULE_COMMON_URL}/City/GetCity`,
    getCityDetail: `${API_BASE_URL}/${MODULE_COMMON_URL}/City/GetCity`,
    addCity: `${API_BASE_URL}/${MODULE_COMMON_URL}/City/AddCity`,
    editCity: `${API_BASE_URL}/${MODULE_COMMON_URL}/City/EditCity`,
    deleteCity: `${API_BASE_URL}/${MODULE_COMMON_URL}/City/DeleteCity`,

    // Department 部門資料管理
    getDepartments: `${API_BASE_URL}/${MODULE_COMMON_URL}/Department/GetDepartment`,
    getDepartmentDetail: `${API_BASE_URL}/${MODULE_COMMON_URL}/Department/GetDepartment`,
    addDepartment: `${API_BASE_URL}/${MODULE_COMMON_URL}/Department/AddDepartment`,
    editDepartment: `${API_BASE_URL}/${MODULE_COMMON_URL}/Department/EditDepartment`,
    deleteDepartment: `${API_BASE_URL}/${MODULE_COMMON_URL}/Department/DeleteDepartment`,

    // District 鄉鎮市區資料管理
    getDistricts: `${API_BASE_URL}/${MODULE_COMMON_URL}/District/GetDistrict`,
    getDistrictDetail: `${API_BASE_URL}/${MODULE_COMMON_URL}/District/GetDistrict`,
    getDistrictByCity: `${API_BASE_URL}/${MODULE_COMMON_URL}/District/GetDistrictByCity`,
    addDistrict: `${API_BASE_URL}/${MODULE_COMMON_URL}/District/AddDistrict`,
    editDistrict: `${API_BASE_URL}/${MODULE_COMMON_URL}/District/EditDistrict`,
    deleteDistrict: `${API_BASE_URL}/${MODULE_COMMON_URL}/District/DeleteDistrict`,

    // Division 組別資料管理
    getDivisions: `${API_BASE_URL}/${MODULE_COMMON_URL}/Division/GetDivision`,
    getDivisionDetail: `${API_BASE_URL}/${MODULE_COMMON_URL}/Division/GetDivision`,
    addDivision: `${API_BASE_URL}/${MODULE_COMMON_URL}/Division/AddDivision`,
    editDivision: `${API_BASE_URL}/${MODULE_COMMON_URL}/Division/EditDivision`,
    deleteDivision: `${API_BASE_URL}/${MODULE_COMMON_URL}/Division/DeleteDivision`,

    // EnterpriseGroups 管理公司群組
    getEnterpriseGroups: `${API_BASE_URL}/${MODULE_COMMON_URL}/EnterpriseGroups/GetEnterpriseGroupsList`,
    getEnterpriseGroupDetail: `${API_BASE_URL}/${MODULE_COMMON_URL}/EnterpriseGroups/GetEnterpriseGroupsDetail`,
    addEnterpriseGroup: `${API_BASE_URL}/${MODULE_COMMON_URL}/EnterpriseGroups/AddEnterpriseGroups`,
    editEnterpriseGroup: `${API_BASE_URL}/${MODULE_COMMON_URL}/EnterpriseGroups/EditEnterpriseGroups`,
    deleteEnterpriseGroup: `${API_BASE_URL}/${MODULE_COMMON_URL}/EnterpriseGroups/DeleteEnterpriseGroups`,

    // JobRank 職級資料管理
    getJobRanks: `${API_BASE_URL}/${MODULE_COMMON_URL}/JobRank/GetJobRank`,
    getJobRankDetail: `${API_BASE_URL}/${MODULE_COMMON_URL}/JobRank/GetJobRank`,
    addJobRank: `${API_BASE_URL}/${MODULE_COMMON_URL}/JobRank/AddJobRank`,
    editJobRank: `${API_BASE_URL}/${MODULE_COMMON_URL}/JobRank/EditJobRank`,
    deleteJobRank: `${API_BASE_URL}/${MODULE_COMMON_URL}/JobRank/DeleteJobRank`,

    // Position 職稱資料管理
    getPositions: `${API_BASE_URL}/${MODULE_COMMON_URL}/Position/GetPosition`,
    getPositionDetail: `${API_BASE_URL}/${MODULE_COMMON_URL}/Position/GetPosition`,
    addPosition: `${API_BASE_URL}/${MODULE_COMMON_URL}/Position/AddPosition`,
    editPosition: `${API_BASE_URL}/${MODULE_COMMON_URL}/Position/EditPosition`,
    deletePosition: `${API_BASE_URL}/${MODULE_COMMON_URL}/Position/DeletePosition`,

    // Roles 角色管理
    getRoles: `${API_BASE_URL}/${MODULE_COMMON_URL}/Roles/GetRoles`,
    getRoleDetail: `${API_BASE_URL}/${MODULE_COMMON_URL}/Roles/GetRoles`,
    addRole: `${API_BASE_URL}/${MODULE_COMMON_URL}/Roles/AddRoles`,
    editRole: `${API_BASE_URL}/${MODULE_COMMON_URL}/Roles/EditRoles`,
    deleteRole: `${API_BASE_URL}/${MODULE_COMMON_URL}/Roles/DeleteRoles`,

    // RolesPermissions 角色權限管理
    getRolesPermissions: `${API_BASE_URL}/${MODULE_COMMON_URL}/RolesPermissions/GetRolesPermissions`,
    getRolesPermissionDetail: `${API_BASE_URL}/${MODULE_COMMON_URL}/RolesPermissions/GetRolesPermissions`,
    addRolesPermission: `${API_BASE_URL}/${MODULE_COMMON_URL}/RolesPermissions/AddRolesPermissions`,
    editRolesPermission: `${API_BASE_URL}/${MODULE_COMMON_URL}/RolesPermissions/EditRolesPermissions`,
    deleteRolesPermission: `${API_BASE_URL}/${MODULE_COMMON_URL}/RolesPermissions/DeleteRolesPermissions`,

    // SystemGroups 系統群組管理
    getSystemGroups: `${API_BASE_URL}/${MODULE_COMMON_URL}/SystemGroups/GetSystemGroups`,
    addSystemGroup: `${API_BASE_URL}/${MODULE_COMMON_URL}/SystemGroups/AddSystemGroups`,
    editSystemGroup: `${API_BASE_URL}/${MODULE_COMMON_URL}/SystemGroups/EditSystemGroups`,
    deleteSystemGroup: `${API_BASE_URL}/${MODULE_COMMON_URL}/SystemGroups/DeleteSystemmGroups`,

    // SystemMenu 系統選單管理
    getSystemMenus: `${API_BASE_URL}/${MODULE_COMMON_URL}/SystemMenu/GetSystemMenu`,
    getSystemMenuDetail: `${API_BASE_URL}/${MODULE_COMMON_URL}/SystemMenu/GetSystemMenu`,
    addSystemMenu: `${API_BASE_URL}/${MODULE_COMMON_URL}/SystemMenu/AddSystemMenu`,
    editSystemMenu: `${API_BASE_URL}/${MODULE_COMMON_URL}/SystemMenu/EditSystemMenu`,
    deleteSystemMenu: `${API_BASE_URL}/${MODULE_COMMON_URL}/SystemMenu/DeleteSystemMenu`,
    getAllMenu: `${API_BASE_URL}/${MODULE_COMMON_URL}/SystemMenu/GetAllMenu`,
    getMyAllMenu: `${API_BASE_URL}/${MODULE_COMMON_URL}/SystemMenu/GetMyAllMenu`,

    // Unit 單位資料管理
    getUnits: `${API_BASE_URL}/${MODULE_COMMON_URL}/Unit/GetAll`,
    getUnitById: `${API_BASE_URL}/${MODULE_COMMON_URL}/Unit/Get`,
    addUnit: `${API_BASE_URL}/${MODULE_COMMON_URL}/Unit/Add`,
    editUnit: `${API_BASE_URL}/${MODULE_COMMON_URL}/Unit/Edit`,
    deleteUnit: `${API_BASE_URL}/${MODULE_COMMON_URL}/Unit/Delete`,
    sortUnit: `${API_BASE_URL}/${MODULE_COMMON_URL}/Unit/Sort`,

    // 訊息相關
    getUnreadMessages: `${API_BASE_URL}/${MODULE_COMMON_URL}/Messages/GetUnreadMessages`,
    getReadMessages: `${API_BASE_URL}/${MODULE_COMMON_URL}/Messages/GetReadMessages`,
    markMessageAsRead: `${API_BASE_URL}/${MODULE_COMMON_URL}/Messages/MarkAsRead`,

    // 通知相關
    getSystemNotifications: `${API_BASE_URL}/${MODULE_COMMON_URL}/Notifications/GetSystemNotifications`,
    getPersonalNotifications: `${API_BASE_URL}/${MODULE_COMMON_URL}/Notifications/GetPersonalNotifications`,
    markNotificationAsRead: `${API_BASE_URL}/${MODULE_COMMON_URL}/Notifications/MarkAsRead`,

    // 公司圖片
    getEnterpriseImages: `${API_BASE_URL}/${MODULE_COMMON_URL}/EnterpriseImage/GetList`,
    getEnterpriseImage: `${API_BASE_URL}/${MODULE_COMMON_URL}/EnterpriseImage/Get`,
    uploadEnterpriseImage: `${API_BASE_URL}/${MODULE_COMMON_URL}/EnterpriseImage/Upload`,
    deleteEnterpriseImage: `${API_BASE_URL}/${MODULE_COMMON_URL}/EnterpriseImage/Delete`,

    /**
     * Pms 財產管理
     * **/
    // AccessoryEquipment 附屬設備管理
    getAccessoryEquipments: `${API_BASE_URL}/${MODULE_PMS_URL}/AccessoryEquipment/GetAll`,
    getAccessoryEquipmentDetail: `${API_BASE_URL}/${MODULE_PMS_URL}/AccessoryEquipment/Get`,
    addAccessoryEquipment: `${API_BASE_URL}/${MODULE_PMS_URL}/AccessoryEquipment/Add`,
    editAccessoryEquipment: `${API_BASE_URL}/${MODULE_PMS_URL}/AccessoryEquipment/Edit`,
    deleteAccessoryEquipment: `${API_BASE_URL}/${MODULE_PMS_URL}/AccessoryEquipment/Delete`,

    // AssetAccount 財產科目管理
    getAssetAccounts: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetAccount/GetAll`,
    getAssetAccountDetail: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetAccount/Get`,
    addAssetAccount: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetAccount/Add`,
    editAssetAccount: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetAccount/Edit`,
    deleteAssetAccount: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetAccount/Delete`,

    // AssetCategory 財產類別管理
    getAssetCategories: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetCategory/GetAll`,
    getAssetCategoryDetail: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetCategory/Get`,
    addAssetCategory: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetCategory/Add`,
    editAssetCategory: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetCategory/Edit`,
    deleteAssetCategory: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetCategory/Delete`,

    // Asset 財產資料管理
    getAssets: `${API_BASE_URL}/${MODULE_PMS_URL}/Asset/GetAll`,
    getAssetDetail: `${API_BASE_URL}/${MODULE_PMS_URL}/Asset/Get`,
    addAsset: `${API_BASE_URL}/${MODULE_PMS_URL}/Asset/Add`,
    editAsset: `${API_BASE_URL}/${MODULE_PMS_URL}/Asset/Edit`,
    deleteAsset: `${API_BASE_URL}/${MODULE_PMS_URL}/Asset/Delete`,
    getNewAssetNo: `${API_BASE_URL}/${MODULE_PMS_URL}/Asset/GetNewAssetNo`,
    validateExcelFile: `${API_BASE_URL}/${MODULE_PMS_URL}/Asset/ValidateExcelFile`,
    batchImport: `${API_BASE_URL}/${MODULE_PMS_URL}/Asset/BatchImport`,
    cancelBatchImport: `${API_BASE_URL}/${MODULE_PMS_URL}/Asset/CancelBatchImport`,
    downloadBatchTemplate: `${API_BASE_URL}/${MODULE_PMS_URL}/Asset/DownloadBatchTemplate`,

    // AssetSource 財產來源管理
    getAssetSources: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetSource/GetAll`,
    getAssetSourceDetail: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetSource/Get`,
    addAssetSource: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetSource/Add`,
    editAssetSource: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetSource/Edit`,
    deleteAssetSource: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetSource/Delete`,

    // DepreciationFormDetail 折舊紀錄管理
    getDepreciations: `${API_BASE_URL}/${MODULE_PMS_URL}/DepreciationFormDetail/GetAll`,
    getDepreciationDetail: `${API_BASE_URL}/${MODULE_PMS_URL}/DepreciationFormDetail/Get`,
    addDepreciation: `${API_BASE_URL}/${MODULE_PMS_URL}/DepreciationFormDetail/Add`,
    editDepreciation: `${API_BASE_URL}/${MODULE_PMS_URL}/DepreciationFormDetail/Edit`,
    deleteDepreciation: `${API_BASE_URL}/${MODULE_PMS_URL}/DepreciationFormDetail/Delete`,
    getDecliningBalanceRate: `${API_BASE_URL}/${MODULE_PMS_URL}/DepreciationFormDetail/DecliningBalanceRate`,

    // DepreciationForm 固定資產折舊單管理
    getDepreciationForms: `${API_BASE_URL}/${MODULE_PMS_URL}/DepreciationForm/GetAll`,
    getAllDepreciationForms: `${API_BASE_URL}/${MODULE_PMS_URL}/DepreciationForm/GetAll`,
    addDepreciationForm: `${API_BASE_URL}/${MODULE_PMS_URL}/DepreciationForm/Add`,
    editDepreciationForm: `${API_BASE_URL}/${MODULE_PMS_URL}/DepreciationForm/Edit`,
    deleteDepreciationForm: `${API_BASE_URL}/${MODULE_PMS_URL}/DepreciationForm/Delete`,
    getDepreciationFormDetail: `${API_BASE_URL}/${MODULE_PMS_URL}/DepreciationForm/details`,
    checkDepreciationFormUsage: `${API_BASE_URL}/${MODULE_PMS_URL}/DepreciationForm/check`,
    // Depreciation Report 財產折舊表
    generateDepreciationReport: `${API_BASE_URL}/${MODULE_PMS_URL}/DepreciationForm/report/generate`,

    // InsuranceUnit 保險單位資料管理
    getInsuranceUnits: `${API_BASE_URL}/${MODULE_PMS_URL}/InsuranceUnit/GetAll`,
    getInsuranceUnitDetail: `${API_BASE_URL}/${MODULE_PMS_URL}/InsuranceUnit/Get`,
    addInsuranceUnit: `${API_BASE_URL}/${MODULE_PMS_URL}/InsuranceUnit/Add`,
    editInsuranceUnit: `${API_BASE_URL}/${MODULE_PMS_URL}/InsuranceUnit/Edit`,
    deleteInsuranceUnit: `${API_BASE_URL}/${MODULE_PMS_URL}/InsuranceUnit/Delete`,

    // Manufacturer 製造商資料管理
    getManufacturers: `${API_BASE_URL}/${MODULE_PMS_URL}/Manufacturer/GetAll`,
    getManufacturerDetail: `${API_BASE_URL}/${MODULE_PMS_URL}/Manufacturer/Get`,
    addManufacturer: `${API_BASE_URL}/${MODULE_PMS_URL}/Manufacturer/Add`,
    editManufacturer: `${API_BASE_URL}/${MODULE_PMS_URL}/Manufacturer/Edit`,
    deleteManufacturer: `${API_BASE_URL}/${MODULE_PMS_URL}/Manufacturer/Delete`,

    // StorageLocation 存放地點資料管理
    getStorageLocations: `${API_BASE_URL}/${MODULE_PMS_URL}/StorageLocation/GetAll`,
    getStorageLocationDetail: `${API_BASE_URL}/${MODULE_PMS_URL}/StorageLocation/Get`,
    addStorageLocation: `${API_BASE_URL}/${MODULE_PMS_URL}/StorageLocation/Add`,
    editStorageLocation: `${API_BASE_URL}/${MODULE_PMS_URL}/StorageLocation/Edit`,
    deleteStorageLocation: `${API_BASE_URL}/${MODULE_PMS_URL}/StorageLocation/Delete`,
    checkStorageCapacity: `${API_BASE_URL}/${MODULE_PMS_URL}/StorageLocation/CheckStorageCapacity`,

    //AmortizationSource 攤提來源管理
    getAmortizationSources: `${API_BASE_URL}/${MODULE_PMS_URL}/AmortizationSource/GetAll`,
    getAmortizationSourceDetail: `${API_BASE_URL}/${MODULE_PMS_URL}/AmortizationSource/Get`,
    addAmortizationSource: `${API_BASE_URL}/${MODULE_PMS_URL}/AmortizationSource/Add`,
    editAmortizationSource: `${API_BASE_URL}/${MODULE_PMS_URL}/AmortizationSource/Edit`,
    deleteAmortizationSource: `${API_BASE_URL}/${MODULE_PMS_URL}/AmortizationSource/Delete`,

    // AssetSubAccount 財產子目管理
    getAssetSubAccounts: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetSubAccount/GetAll`,
    getAssetSubAccountDetail: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetSubAccount/Get`,
    addAssetSubAccount: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetSubAccount/Add`,
    editAssetSubAccount: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetSubAccount/Edit`,
    deleteAssetSubAccount: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetSubAccount/Delete`,

    // AssetStatus 財產狀態管理
    getAssetStatuses: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetStatus/GetAll`,
    getAssetStatusDetail: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetStatus/Get`,
    addAssetStatus: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetStatus/Add`,
    editAssetStatus: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetStatus/Edit`,
    deleteAssetStatus: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetStatus/Delete`,

    // EquipmentType 設備類型
    getEquipmentTypes: `${API_BASE_URL}/${MODULE_PMS_URL}/EquipmentType/GetAll`,
    getEquipmentTypeDetail: `${API_BASE_URL}/${MODULE_PMS_URL}/EquipmentType/Get`,
    addEquipmentType: `${API_BASE_URL}/${MODULE_PMS_URL}/EquipmentType/Add`,
    editEquipmentType: `${API_BASE_URL}/${MODULE_PMS_URL}/EquipmentType/Edit`,
    deleteEquipmentType: `${API_BASE_URL}/${MODULE_PMS_URL}/EquipmentType/Delete`,

    // PmsUserRole 財產系統使用者身分管理
    getPmsUserRoles: `${API_BASE_URL}/${MODULE_PMS_URL}/PmsUserRole/GetAll`,
    getPmsUserRoleDetail: `${API_BASE_URL}/${MODULE_PMS_URL}/PmsUserRole/Get`,
    addPmsUserRole: `${API_BASE_URL}/${MODULE_PMS_URL}/PmsUserRole/Add`,
    editPmsUserRole: `${API_BASE_URL}/${MODULE_PMS_URL}/PmsUserRole/Edit`,
    deletePmsUserRole: `${API_BASE_URL}/${MODULE_PMS_URL}/PmsUserRole/Delete`,
    getUserRoles: `${API_BASE_URL}/${MODULE_PMS_URL}/PmsUserRole/GetUserRoles`,
    assignRoleToUser: `${API_BASE_URL}/${MODULE_PMS_URL}/PmsUserRole/AssignRoleToUser`,
    removeRoleFromUser: `${API_BASE_URL}/${MODULE_PMS_URL}/PmsUserRole/RemoveRoleFromUser`,

    // PmsSystemParameter 系統參數設定
    getPmsSystemParameters: `${API_BASE_URL}/${MODULE_PMS_URL}/PmsSystemParameter/GetAll`,
    getPmsSystemParametersByType: `${API_BASE_URL}/${MODULE_PMS_URL}/PmsSystemParameter/GetByType`,
    getPmsSystemParameterDetail: `${API_BASE_URL}/${MODULE_PMS_URL}/PmsSystemParameter/Get`,
    addPmsSystemParameter: `${API_BASE_URL}/${MODULE_PMS_URL}/PmsSystemParameter/Add`,
    editPmsSystemParameter: `${API_BASE_URL}/${MODULE_PMS_URL}/PmsSystemParameter/Edit`,
    deletePmsSystemParameter: `${API_BASE_URL}/${MODULE_PMS_URL}/PmsSystemParameter/Delete`,
    getDepreciationMethods: `${API_BASE_URL}/${MODULE_PMS_URL}/PmsSystemParameter/GetDepreciationMethods`,
    setDefaultDepreciationMethod: `${API_BASE_URL}/${MODULE_PMS_URL}/PmsSystemParameter/SetDefaultDepreciationMethod`,
    getDecliningBalanceRates: `${API_BASE_URL}/${MODULE_PMS_URL}/PmsSystemParameter/GetDecliningBalanceRates`,
    setDecliningBalanceRate: `${API_BASE_URL}/${MODULE_PMS_URL}/PmsSystemParameter/SetDecliningBalanceRate`,
    getInitializationStatus: `${API_BASE_URL}/${MODULE_PMS_URL}/PmsSystemParameter/GetInitializationStatus`,
    setInitializationStatus: `${API_BASE_URL}/${MODULE_PMS_URL}/PmsSystemParameter/SetInitializationStatus`,

    // 財產科目折舊方法設定
    getAssetAccountDepreciationMethods: `${API_BASE_URL}/${MODULE_PMS_URL}/PmsSystemParameter/GetAssetAccountDepreciationMethods`,
    getAssetAccountDepreciationMethod: `${API_BASE_URL}/${MODULE_PMS_URL}/PmsSystemParameter/GetAssetAccountDepreciationMethod`,
    setAssetAccountDepreciationMethod: `${API_BASE_URL}/${MODULE_PMS_URL}/PmsSystemParameter/SetAssetAccountDepreciationMethod`,

    // AssetChangeReport 財產增減表
    generateAssetChangeReport: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetChangeReport/generate`,
    generateAssetChangeReportDetail: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetChangeReport/generate-detail`,
    getOpeningBalance: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetChangeReport/opening-balance`,
    getPeriodIncrease: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetChangeReport/period-increase`,
    getPeriodDecrease: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetChangeReport/period-decrease`,
    getPeriodDepreciation: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetChangeReport/period-depreciation`,
    getChangeDetails: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetChangeReport/change-details`,

    // CustodianRoster 保管人&使用人清冊
    generateCustodianRoster: `${API_BASE_URL}/${MODULE_PMS_URL}/CustodianRoster/generate`,

    // VendorMaintenance 廠商修繕單管理
    getVendorMaintenances: `${API_BASE_URL}/${MODULE_PMS_URL}/VendorMaintenance/GetAll`,
    getVendorMaintenanceById: `${API_BASE_URL}/${MODULE_PMS_URL}/VendorMaintenance`,
    addVendorMaintenance: `${API_BASE_URL}/${MODULE_PMS_URL}/VendorMaintenance/Add`,
    editVendorMaintenance: `${API_BASE_URL}/${MODULE_PMS_URL}/VendorMaintenance/Edit`,
    deleteVendorMaintenance: `${API_BASE_URL}/${MODULE_PMS_URL}/VendorMaintenance/Delete`,
    approveVendorMaintenance: `${API_BASE_URL}/${MODULE_PMS_URL}/VendorMaintenance`,
    assignVendor: `${API_BASE_URL}/${MODULE_PMS_URL}/VendorMaintenance`,
    startWork: `${API_BASE_URL}/${MODULE_PMS_URL}/VendorMaintenance`,
    completeMaintenance: `${API_BASE_URL}/${MODULE_PMS_URL}/VendorMaintenance`,
    inspectMaintenance: `${API_BASE_URL}/${MODULE_PMS_URL}/VendorMaintenance`,
    closeMaintenance: `${API_BASE_URL}/${MODULE_PMS_URL}/VendorMaintenance`,
    cancelMaintenance: `${API_BASE_URL}/${MODULE_PMS_URL}/VendorMaintenance`,
    batchProcessMaintenance: `${API_BASE_URL}/${MODULE_PMS_URL}/VendorMaintenance/batch`,
    getMaintenanceStatistics: `${API_BASE_URL}/${MODULE_PMS_URL}/VendorMaintenance/statistics`,
    getOverdueMaintenance: `${API_BASE_URL}/${MODULE_PMS_URL}/VendorMaintenance/overdue`,

    // AssetInventory 財產盤點管理
    assetInventorySummary: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetInventory/summary`,
    assetInventoryFormList: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetInventory/form-list`,
    assetInventoryCreateForm: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetInventory/form`,
    assetInventoryCheck: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetInventory/check`,
    assetInventoryBulkCheck: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetInventory/bulk-check`,
    assetInventoryReset: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetInventory/reset`,
    assetInventoryCloseForm: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetInventory/form/close`,

    // AssetCarryOut 資產攜出申請管理
    getAssetCarryOuts: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetCarryOut/GetAll`,
    getAssetCarryOutById: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetCarryOut`,
    addAssetCarryOut: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetCarryOut/Add`,
    editAssetCarryOut: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetCarryOut`,
    deleteAssetCarryOut: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetCarryOut/Delete`,
    approveAssetCarryOut: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetCarryOut`,
    registerCarryOut: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetCarryOut`,
    registerReturn: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetCarryOut`,
    batchProcessCarryOut: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetCarryOut/batch`,

    // AssetLocationTransfer 財產位置變動單管理
    getAssetLocationTransfers: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetLocationTransfer`,
    getAssetLocationTransferById: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetLocationTransfer`,
    getAssetLocationTransferByNo: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetLocationTransfer/by-no`,
    addAssetLocationTransfer: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetLocationTransfer/Add`,
    editAssetLocationTransfer: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetLocationTransfer`,
    updateAssetLocationTransfer: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetLocationTransfer/Update`,
    deleteAssetLocationTransfer: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetLocationTransfer/Delete`,
    approveAssetLocationTransfer: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetLocationTransfer`,
    executeAssetLocationTransfer: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetLocationTransfer`,
    getAssetCurrentLocation: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetLocationTransfer/asset`,
    validateAssetTransfer: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetLocationTransfer/asset`,
    generateTransferNumber: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetLocationTransfer/generate-transfer-no`,
    getTransferDetails: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetLocationTransfer`,
    checkPendingTransfer: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetLocationTransfer/asset`,
    getPendingApprovalCount: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetLocationTransfer/pending-approval-count`,
    getPendingExecutionCount: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetLocationTransfer/pending-execution-count`,
    batchApproveTransfers: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetLocationTransfer/batch-approve`,
    batchExecuteTransfers: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetLocationTransfer/batch-execute`,
    getChangeItemsOptions: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetLocationTransfer/change-items`,
    getApprovalStatusOptions: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetLocationTransfer/approval-statuses`,
    getExecutionStatusOptions: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetLocationTransfer/execution-statuses`,
    getCarryOutStatistics: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetCarryOut/statistics`,
    getOverdueCarryOuts: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetCarryOut/overdue`,
    generateCarryOutNumber: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetCarryOut/generate-number`,

    // AssetScrapForm 資產報廢單管理
    getAssetScrapForms: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetScrapForm`,
    getAssetScrapFormById: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetScrapForm`,
    getAssetScrapFormByNo: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetScrapForm/by-no`,
    addAssetScrapForm: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetScrapForm/Add`,
    updateAssetScrapForm: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetScrapForm`,
    deleteAssetScrapForm: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetScrapForm/Delete`,
    approveAssetScrapForm: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetScrapForm`,
    executeAssetScrapForm: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetScrapForm`,
    generateAssetScrapFormNo: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetScrapForm/generate-number`,
    getAvailableAssets: `${API_BASE_URL}/${MODULE_PMS_URL}/AssetScrapForm/available-assets`,

    /**
     * Pas 人事薪資管理
     * **/
    // Employee 員工主檔管理
    getEmployeeList: `${API_BASE_URL}/${MODULE_PAS_URL}/Employee/GetAll`,
    getEmployeeListByEffectiveDate: `${API_BASE_URL}/${MODULE_PAS_URL}/Employee/GetAllByEffectiveDate`,
    getEmployeeDetail: `${API_BASE_URL}/${MODULE_PAS_URL}/Employee/Get`,
    addEmployee: `${API_BASE_URL}/${MODULE_PAS_URL}/Employee/Add`,
    editEmployee: `${API_BASE_URL}/${MODULE_PAS_URL}/Employee/Edit`,
    completeEmployee: `${API_BASE_URL}/${MODULE_PAS_URL}/Employee/Complete`,
    deleteEmployee: `${API_BASE_URL}/${MODULE_PAS_URL}/Employee/Delete`,

    // Education 學歷資料管理
    getEducationList: `${API_BASE_URL}/${MODULE_PAS_URL}/Education/GetAll`,
    getEducationDetail: `${API_BASE_URL}/${MODULE_PAS_URL}/Education/Get`,
    addEducation: `${API_BASE_URL}/${MODULE_PAS_URL}/Education/Add`,
    editEducation: `${API_BASE_URL}/${MODULE_PAS_URL}/Education/Edit`,
    deleteEducation: `${API_BASE_URL}/${MODULE_PAS_URL}/Education/Delete`,

    // Train 教育訓練資料管理
    getTrainList: `${API_BASE_URL}/${MODULE_PAS_URL}/Train/GetAll`,
    getTrainDetail: `${API_BASE_URL}/${MODULE_PAS_URL}/Train/Get`,
    addTrain: `${API_BASE_URL}/${MODULE_PAS_URL}/Train/Add`,
    editTrain: `${API_BASE_URL}/${MODULE_PAS_URL}/Train/Edit`,
    deleteTrain: `${API_BASE_URL}/${MODULE_PAS_URL}/Train/Delete`,

    // Examination 考試資料管理
    getExaminationList: `${API_BASE_URL}/${MODULE_PAS_URL}/Examination/GetAll`,
    getExaminationDetail: `${API_BASE_URL}/${MODULE_PAS_URL}/Examination/Get`,
    addExamination: `${API_BASE_URL}/${MODULE_PAS_URL}/Examination/Add`,
    editExamination: `${API_BASE_URL}/${MODULE_PAS_URL}/Examination/Edit`,
    deleteExamination: `${API_BASE_URL}/${MODULE_PAS_URL}/Examination/Delete`,

    // Certification 檢覈資料管理
    getCertificationList: `${API_BASE_URL}/${MODULE_PAS_URL}/Certification/GetAll`,
    getCertificationDetail: `${API_BASE_URL}/${MODULE_PAS_URL}/Certification/Get`,
    addCertification: `${API_BASE_URL}/${MODULE_PAS_URL}/Certification/Add`,
    editCertification: `${API_BASE_URL}/${MODULE_PAS_URL}/Certification/Edit`,
    deleteCertification: `${API_BASE_URL}/${MODULE_PAS_URL}/Certification/Delete`,

    // Undergo 歷任經歷資料管理
    getUndergoList: `${API_BASE_URL}/${MODULE_PAS_URL}/Undergo/GetAll`,
    getUndergoDetail: `${API_BASE_URL}/${MODULE_PAS_URL}/Undergo/Get`,
    addUndergo: `${API_BASE_URL}/${MODULE_PAS_URL}/Undergo/Add`,
    editUndergo: `${API_BASE_URL}/${MODULE_PAS_URL}/Undergo/Edit`,
    deleteUndergo: `${API_BASE_URL}/${MODULE_PAS_URL}/Undergo/Delete`,

    // Ensure 保證書資料管理
    getEnsureList: `${API_BASE_URL}/${MODULE_PAS_URL}/Ensure/GetAll`,
    getEnsureDetail: `${API_BASE_URL}/${MODULE_PAS_URL}/Ensure/Get`,
    addEnsure: `${API_BASE_URL}/${MODULE_PAS_URL}/Ensure/Add`,
    editEnsure: `${API_BASE_URL}/${MODULE_PAS_URL}/Ensure/Edit`,
    deleteEnsure: `${API_BASE_URL}/${MODULE_PAS_URL}/Ensure/Delete`,

    // Suspend 留職停薪資料管理
    getSuspendList: `${API_BASE_URL}/${MODULE_PAS_URL}/Suspend/GetAll`,
    getSuspendDetail: `${API_BASE_URL}/${MODULE_PAS_URL}/Suspend/Get`,
    addSuspend: `${API_BASE_URL}/${MODULE_PAS_URL}/Suspend/Add`,
    editSuspend: `${API_BASE_URL}/${MODULE_PAS_URL}/Suspend/Edit`,
    deleteSuspend: `${API_BASE_URL}/${MODULE_PAS_URL}/Suspend/Delete`,

    // Salary 薪資主檔管理
    getSalaryDetail: `${API_BASE_URL}/${MODULE_PAS_URL}/Salary/Get`, // GET
    editSalary: `${API_BASE_URL}/${MODULE_PAS_URL}/Salary/Edit`,

    // Hensure 眷屬依附資料管理
    getHensureList: `${API_BASE_URL}/${MODULE_PAS_URL}/Hensure/GetAll`,
    getHensureDetail: `${API_BASE_URL}/${MODULE_PAS_URL}/Hensure/Get`,
    addHensure: `${API_BASE_URL}/${MODULE_PAS_URL}/Hensure/Add`,
    editHensure: `${API_BASE_URL}/${MODULE_PAS_URL}/Hensure/Edit`,
    deleteHensure: `${API_BASE_URL}/${MODULE_PAS_URL}/Hensure/Delete`,

    // Dependent 扶養資料管理
    getDependentList: `${API_BASE_URL}/${MODULE_PAS_URL}/Dependent/GetAll`,
    getDependentDetail: `${API_BASE_URL}/${MODULE_PAS_URL}/Dependent/Get`,
    addDependent: `${API_BASE_URL}/${MODULE_PAS_URL}/Dependent/Add`,
    editDependent: `${API_BASE_URL}/${MODULE_PAS_URL}/Dependent/Edit`,
    deleteDependent: `${API_BASE_URL}/${MODULE_PAS_URL}/Dependent/Delete`,

    // PerformancePointGroup 點數群組管理
    getPerformancePointGroupList: `${API_BASE_URL}/${MODULE_PAS_URL}/PerformancePointGroup/GetAll`,
    getPerformancePointGroupDetail: `${API_BASE_URL}/${MODULE_PAS_URL}/PerformancePointGroup/Get`,
    addPerformancePointGroup: `${API_BASE_URL}/${MODULE_PAS_URL}/PerformancePointGroup/Add`,
    editPerformancePointGroup: `${API_BASE_URL}/${MODULE_PAS_URL}/PerformancePointGroup/Edit`,
    deletePerformancePointGroup: `${API_BASE_URL}/${MODULE_PAS_URL}/PerformancePointGroup/Delete`,

    // PerformancePointType 點數類型管理
    getPerformancePointTypeList: `${API_BASE_URL}/${MODULE_PAS_URL}/PerformancePointType/GetByGroup`,
    getPerformancePointTypeDetail: `${API_BASE_URL}/${MODULE_PAS_URL}/PerformancePointType/Get`,
    addPerformancePointType: `${API_BASE_URL}/${MODULE_PAS_URL}/PerformancePointType/Add`,
    editPerformancePointType: `${API_BASE_URL}/${MODULE_PAS_URL}/PerformancePointType/Edit`,
    deletePerformancePointType: `${API_BASE_URL}/${MODULE_PAS_URL}/PerformancePointType/Delete`,
    getGroupTypeCascaderOptions: `${API_BASE_URL}/${MODULE_PAS_URL}/PerformancePointType/GetGroupTypeCascaderOptions`,

    // PerformancePointRecord 員工點數紀錄管理
    getPerformancePointRecordList: `${API_BASE_URL}/${MODULE_PAS_URL}/PerformancePointRecord/GetByUser`,
    getPerformancePointRecordDetail: `${API_BASE_URL}/${MODULE_PAS_URL}/PerformancePointRecord/Get`,
    addPerformancePointRecord: `${API_BASE_URL}/${MODULE_PAS_URL}/PerformancePointRecord/Add`,
    editPerformancePointRecord: `${API_BASE_URL}/${MODULE_PAS_URL}/PerformancePointRecord/Edit`,
    deletePerformancePointRecord: `${API_BASE_URL}/${MODULE_PAS_URL}/PerformancePointRecord/Delete`,
    getPerformancePointSummary: `${API_BASE_URL}/${MODULE_PAS_URL}/PerformancePointRecord/GetPerformancePointSummary`,

    // RegularSalaryItem 常態薪資項目管理
    getRegularSalaryItemList: `${API_BASE_URL}/${MODULE_PAS_URL}/RegularSalaryItem/GetAll`,
    getRegularSalaryItemDetail: `${API_BASE_URL}/${MODULE_PAS_URL}/RegularSalaryItem/Get`,
    addRegularSalaryItem: `${API_BASE_URL}/${MODULE_PAS_URL}/RegularSalaryItem/Add`,
    editRegularSalaryItem: `${API_BASE_URL}/${MODULE_PAS_URL}/RegularSalaryItem/Edit`,
    deleteRegularSalaryItem: `${API_BASE_URL}/${MODULE_PAS_URL}/RegularSalaryItem/Delete`,
    getSalaryItemTypeOptions: `${API_BASE_URL}/${MODULE_PAS_URL}/RegularSalaryItem/GetSalaryItemTypeOptions`,

    // EmployeeRegularSalary 員工常態薪資管理
    getEmployeeRegularSalaryList: `${API_BASE_URL}/${MODULE_PAS_URL}/EmployeeRegularSalary/GetByUser`,
    getEmployeeRegularSalaryDetail: `${API_BASE_URL}/${MODULE_PAS_URL}/EmployeeRegularSalary/Get`,
    addEmployeeRegularSalary: `${API_BASE_URL}/${MODULE_PAS_URL}/EmployeeRegularSalary/Add`,
    editEmployeeRegularSalary: `${API_BASE_URL}/${MODULE_PAS_URL}/EmployeeRegularSalary/Edit`,
    deleteEmployeeRegularSalary: `${API_BASE_URL}/${MODULE_PAS_URL}/EmployeeRegularSalary/Delete`,

    // MonthlySalary 月薪資料管理
    generateMonthlySalary: `${API_BASE_URL}/${MODULE_PAS_URL}/MonthlySalary/Generate`,
    checkMonthlySalaryDuplicate: `${API_BASE_URL}/${MODULE_PAS_URL}/MonthlySalary/CheckDuplicate`,
    getMonthlySalaryList: `${API_BASE_URL}/${MODULE_PAS_URL}/MonthlySalary/GetList`,
    getMonthlySalaryDetail: `${API_BASE_URL}/${MODULE_PAS_URL}/MonthlySalary/Get`,
    addMonthlySalary: `${API_BASE_URL}/${MODULE_PAS_URL}/MonthlySalary/Add`,
    editMonthlySalary: `${API_BASE_URL}/${MODULE_PAS_URL}/MonthlySalary/Edit`,
    deleteMonthlySalary: `${API_BASE_URL}/${MODULE_PAS_URL}/MonthlySalary/Delete`,
    deleteMonthlySalaryByMonth: `${API_BASE_URL}/${MODULE_PAS_URL}/MonthlySalary/DeleteByMonth`,

    getSalaryAdjustmentList: `${API_BASE_URL}/${MODULE_PAS_URL}/MonthlySalary/Adjustment/GetList`,
    addSalaryAdjustment: `${API_BASE_URL}/${MODULE_PAS_URL}/MonthlySalary/Adjustment/Add`,
    editSalaryAdjustment: `${API_BASE_URL}/${MODULE_PAS_URL}/MonthlySalary/Adjustment/Edit`,
    deleteSalaryAdjustment: `${API_BASE_URL}/${MODULE_PAS_URL}/MonthlySalary/Adjustment/Delete`,

    getHensureInsuranceList: `${API_BASE_URL}/${MODULE_PAS_URL}/MonthlySalary/HensureInsurance/GetList`,
    addHensureInsurance: `${API_BASE_URL}/${MODULE_PAS_URL}/MonthlySalary/HensureInsurance/Add`,
    editHensureInsurance: `${API_BASE_URL}/${MODULE_PAS_URL}/MonthlySalary/HensureInsurance/Edit`,
    deleteHensureInsurance: `${API_BASE_URL}/${MODULE_PAS_URL}/MonthlySalary/HensureInsurance/Delete`,

    // Bonus 獎金資料管理
    generateBonus: `${API_BASE_URL}/${MODULE_PAS_URL}/Bonus/Generate`,
    checkBonusDuplicate: `${API_BASE_URL}/${MODULE_PAS_URL}/Bonus/CheckDuplicate`,
    getBonusList: `${API_BASE_URL}/${MODULE_PAS_URL}/Bonus/GetList`,
    getBonusDetail: `${API_BASE_URL}/${MODULE_PAS_URL}/Bonus/Get`,
    addBonus: `${API_BASE_URL}/${MODULE_PAS_URL}/Bonus/Add`,
    editBonus: `${API_BASE_URL}/${MODULE_PAS_URL}/Bonus/Edit`,
    deleteBonus: `${API_BASE_URL}/${MODULE_PAS_URL}/Bonus/Delete`,
    deleteBonusByMonth: `${API_BASE_URL}/${MODULE_PAS_URL}/Bonus/DeleteByMonth`,

    // BackPay 補薪資料管理
    generateBackPay: `${API_BASE_URL}/${MODULE_PAS_URL}/BackPay/Generate`,
    checkBackPayDuplicate: `${API_BASE_URL}/${MODULE_PAS_URL}/BackPay/CheckDuplicate`,
    getBackPayList: `${API_BASE_URL}/${MODULE_PAS_URL}/BackPay/GetList`,
    getBackPayDetail: `${API_BASE_URL}/${MODULE_PAS_URL}/BackPay/Get`,
    addBackPay: `${API_BASE_URL}/${MODULE_PAS_URL}/BackPay/Add`,
    editBackPay: `${API_BASE_URL}/${MODULE_PAS_URL}/BackPay/Edit`,
    deleteBackPay: `${API_BASE_URL}/${MODULE_PAS_URL}/BackPay/Delete`,
    deleteBackPayByIssueDate: `${API_BASE_URL}/${MODULE_PAS_URL}/BackPay/DeleteByIssueDate`,

    // SalaryPoint 薪點資料管理
    getSalaryPointList: `${API_BASE_URL}/${MODULE_PAS_URL}/SalaryPoint/GetAll`,
    getSalaryPointDetail: `${API_BASE_URL}/${MODULE_PAS_URL}/SalaryPoint/Get`,
    addSalaryPoint: `${API_BASE_URL}/${MODULE_PAS_URL}/SalaryPoint/Add`,
    editSalaryPoint: `${API_BASE_URL}/${MODULE_PAS_URL}/SalaryPoint/Edit`,
    deleteSalaryPoint: `${API_BASE_URL}/${MODULE_PAS_URL}/SalaryPoint/Delete`,
    getSalaryPointAmountByDate: `${API_BASE_URL}/${MODULE_PAS_URL}/SalaryPoint/GetAmountByDate`, //全域薪點金額
    getSalaryPointAmountByUserId: `${API_BASE_URL}/${MODULE_PAS_URL}/SalaryPoint/GetEffectiveSalaryPointAmountByUserId`, //依員工取得有效薪點金額

    // DepartmentSalaryPoint 部門薪點資料管理
    getDepartmentSalaryPointList: `${API_BASE_URL}/${MODULE_PAS_URL}/DepartmentSalaryPoint/GetAll`,
    getDepartmentSalaryPointListByDepartment: `${API_BASE_URL}/${MODULE_PAS_URL}/DepartmentSalaryPoint/GetByDepartment`,
    getDepartmentSalaryPointDetail: `${API_BASE_URL}/${MODULE_PAS_URL}/DepartmentSalaryPoint/Get`,
    addDepartmentSalaryPoint: `${API_BASE_URL}/${MODULE_PAS_URL}/DepartmentSalaryPoint/Add`,
    editDepartmentSalaryPoint: `${API_BASE_URL}/${MODULE_PAS_URL}/DepartmentSalaryPoint/Edit`,
    deleteDepartmentSalaryPoint: `${API_BASE_URL}/${MODULE_PAS_URL}/DepartmentSalaryPoint/Delete`,
    getDepartmentSalaryPointAmountByDate: `${API_BASE_URL}/${MODULE_PAS_URL}/DepartmentSalaryPoint/GetAmountByDate`, //部門薪點金額

    // InsuranceGrade 保險級距管理
    getInsuranceGradeList: `${API_BASE_URL}/${MODULE_PAS_URL}/InsuranceGrade/GetInsuranceGradeList`,
    getInsuranceGradeDetail: `${API_BASE_URL}/${MODULE_PAS_URL}/InsuranceGrade/GetInsuranceGradeDetail`,
    addInsuranceGrade: `${API_BASE_URL}/${MODULE_PAS_URL}/InsuranceGrade/Add`,
    editInsuranceGrade: `${API_BASE_URL}/${MODULE_PAS_URL}/InsuranceGrade/Edit`,
    deleteInsuranceGrade: `${API_BASE_URL}/${MODULE_PAS_URL}/InsuranceGrade/Delete`,
    getInsuranceGradeBySalary: `${API_BASE_URL}/${MODULE_PAS_URL}/InsuranceGrade/by-salary`,
    getEmployeeInsuranceGradeDetail: `${API_BASE_URL}/${MODULE_PAS_URL}/InsuranceGrade/GetEmployeeInsuranceGradeDetail`,

    // InsuranceHistory 保險級距歷程管理
    getInsuranceHistoryDetail: `${API_BASE_URL}/${MODULE_PAS_URL}/InsuranceHistory/Get`,
    getInsuranceHistoryByUserAndType: `${API_BASE_URL}/${MODULE_PAS_URL}/InsuranceHistory/GetByUserAndType`,
    getEffectiveInsuranceGrade: `${API_BASE_URL}/${MODULE_PAS_URL}/InsuranceHistory/GetEffectiveGrade`,
    getAllEffectiveInsuranceGrades: `${API_BASE_URL}/${MODULE_PAS_URL}/InsuranceHistory/GetAllEffectiveGrades`,

    addInsuranceHistory: `${API_BASE_URL}/${MODULE_PAS_URL}/InsuranceHistory/Add`,
    editInsuranceHistory: `${API_BASE_URL}/${MODULE_PAS_URL}/InsuranceHistory/Edit`,
    deleteInsuranceHistory: `${API_BASE_URL}/${MODULE_PAS_URL}/InsuranceHistory/Delete`,

    // PasOptionParameter 參數選單設定
    getJobtitleOptions: `${API_BASE_URL}/${MODULE_PAS_URL}/PasOptionParameter/GetJobtitleOptions`, // 取得職稱選項.
    getIdTypeOptions: `${API_BASE_URL}/${MODULE_PAS_URL}/PasOptionParameter/GetIdTypeOptions`, // 取得證號別選項.
    getIdErrorOptions: `${API_BASE_URL}/${MODULE_PAS_URL}/PasOptionParameter/GetIdErrorOptions`, // 取得證號錯誤註記選項.
    getBloodTypeOptions: `${API_BASE_URL}/${MODULE_PAS_URL}/PasOptionParameter/GetBloodTypeOptions`, // 取得血型選項.
    getDegreeTypeOptions: `${API_BASE_URL}/${MODULE_PAS_URL}/PasOptionParameter/GetDegreeTypeOptions`, // 取得學位類型選項.
    getGraduateOptions: `${API_BASE_URL}/${MODULE_PAS_URL}/PasOptionParameter/GetGraduateOptions`, // 取得結業類型選項.
    getSuspendTypeOptions: `${API_BASE_URL}/${MODULE_PAS_URL}/PasOptionParameter/GetSuspendTypeOptions`, // 取得留停類型選項.
    getSuspendKindOptions: `${API_BASE_URL}/${MODULE_PAS_URL}/PasOptionParameter/GetSuspendKindOptions`, // 取得留停種類選項.
    getEmployeeContributionOptions: `${API_BASE_URL}/${MODULE_PAS_URL}/PasOptionParameter/GetEmployeeContributionOptions`, // 取得員工自提額類型選項.
    getIncomeTaxTypeOptions: `${API_BASE_URL}/${MODULE_PAS_URL}/PasOptionParameter/GetIncomeTaxTypeOptions`, // 取得計稅型式類型選項.
    getPayoffTypeOptions: `${API_BASE_URL}/${MODULE_PAS_URL}/PasOptionParameter/GetPayoffTypeOptions`, // 取得發薪狀況選項.
    getDepTypeOptions: `${API_BASE_URL}/${MODULE_PAS_URL}/PasOptionParameter/GetDepTypeOptions`, // 取得稱謂關係選項.
    getRegularSalaryCreaseTypeOptions: `${API_BASE_URL}/${MODULE_PAS_URL}/PasOptionParameter/GetRegularSalaryCreaseTypeOptions`, // 取得加減薪項目類型選項.
    getJobroleTypeOptions: `${API_BASE_URL}/${MODULE_PAS_URL}/PasOptionParameter/GetJobroleTypeOptions`, // 取得任用資格選項.
    getSalaryTypeOptions: `${API_BASE_URL}/${MODULE_PAS_URL}/PasOptionParameter/GetSalaryTypeOptions`, // 取得薪俸類型選項.
    getCategoryTypeOptions: `${API_BASE_URL}/${MODULE_PAS_URL}/PasOptionParameter/GetCategoryTypeOptions`, // 取得錄用類別選項.
    getJobLevelOptions: `${API_BASE_URL}/${MODULE_PAS_URL}/PasOptionParameter/GetJobLevelOptions`, // 取得職等選項.
    getJobRankOptions: `${API_BASE_URL}/${MODULE_PAS_URL}/PasOptionParameter/GetJobRankOptions`, // 取得級數選項.
    getPromotionTypeOptions: `${API_BASE_URL}/${MODULE_PAS_URL}/PasOptionParameter/GetPromotionTypeOptions`,
    getAllowanceTypeOptions: `${API_BASE_URL}/${MODULE_PAS_URL}/PasOptionParameter/GetAllowanceTypeOptions`, // 取得升遷類型選項.
    getBonusTypeOptions: `${API_BASE_URL}/${MODULE_PAS_URL}/PasOptionParameter/GetBonusTypeOptions`, // 取得獎金類型選項.

    // TaxRate 薪資稅率設定管理
    getGlobalTaxRate: `${API_BASE_URL}/${MODULE_PAS_URL}/TaxRate/GetGlobalTaxRate`,
    setGlobalTaxRate: `${API_BASE_URL}/${MODULE_PAS_URL}/TaxRate/SetGlobalTaxRate`,
    getProgressiveTaxRates: `${API_BASE_URL}/${MODULE_PAS_URL}/TaxRate/GetProgressiveTaxRates`,
    addProgressiveTaxRate: `${API_BASE_URL}/${MODULE_PAS_URL}/TaxRate/AddProgressiveTaxRate`,
    updateProgressiveTaxRate: `${API_BASE_URL}/${MODULE_PAS_URL}/TaxRate/EditProgressiveTaxRate`,
    deleteProgressiveTaxRate: `${API_BASE_URL}/${MODULE_PAS_URL}/TaxRate/DeleteProgressiveTaxRate`,
    getProgressiveTaxRateDetail: `${API_BASE_URL}/${MODULE_PAS_URL}/TaxRate/GetProgressiveTaxRateByUid`,

    // Promotion 升遷異動資料管理
    getPromotionList: `${API_BASE_URL}/${MODULE_PAS_URL}/Promotion/GetAll`,
    getPromotionDetail: `${API_BASE_URL}/${MODULE_PAS_URL}/Promotion/Get`,
    getLatestPromotion: `${API_BASE_URL}/${MODULE_PAS_URL}/Promotion/GetLatest`,
    addPromotion: `${API_BASE_URL}/${MODULE_PAS_URL}/Promotion/Add`,
    editPromotion: `${API_BASE_URL}/${MODULE_PAS_URL}/Promotion/Edit`,
    deletePromotion: `${API_BASE_URL}/${MODULE_PAS_URL}/Promotion/Delete`,

    // ServiceDepartmentChange 服務部門異動資料管理
    getServiceDepartmentChangeList: `${API_BASE_URL}/${MODULE_PAS_URL}/ServiceDepartmentChange/GetAll`,
    getServiceDepartmentChangeDetail: `${API_BASE_URL}/${MODULE_PAS_URL}/ServiceDepartmentChange/Get`,
    addServiceDepartmentChange: `${API_BASE_URL}/${MODULE_PAS_URL}/ServiceDepartmentChange/Add`,
    editServiceDepartmentChange: `${API_BASE_URL}/${MODULE_PAS_URL}/ServiceDepartmentChange/Edit`,
    deleteServiceDepartmentChange: `${API_BASE_URL}/${MODULE_PAS_URL}/ServiceDepartmentChange/Delete`,

    // ExpenseDepartmentChange 開支部門異動資料管理
    getExpenseDepartmentChangeList: `${API_BASE_URL}/${MODULE_PAS_URL}/ExpenseDepartmentChange/GetAll`,
    getExpenseDepartmentChangeDetail: `${API_BASE_URL}/${MODULE_PAS_URL}/ExpenseDepartmentChange/Get`,
    addExpenseDepartmentChange: `${API_BASE_URL}/${MODULE_PAS_URL}/ExpenseDepartmentChange/Add`,
    editExpenseDepartmentChange: `${API_BASE_URL}/${MODULE_PAS_URL}/ExpenseDepartmentChange/Edit`,
    deleteExpenseDepartmentChange: `${API_BASE_URL}/${MODULE_PAS_URL}/ExpenseDepartmentChange/Delete`,

    // #region Ims進銷存管理系統
    // Item 庫存品管理
    getItemList: `${API_BASE_URL}/${MODULE_IMS_URL}/Item`,            //取得庫存品列表
    getItem: `${API_BASE_URL}/${MODULE_IMS_URL}/Item`,                //取得庫存品 (GET)
    addItem: `${API_BASE_URL}/${MODULE_IMS_URL}/Item`,                //新增庫存品 (POST)
    editItem: `${API_BASE_URL}/${MODULE_IMS_URL}/Item`,               //修改庫存品 (PUT)
    deleteItem: `${API_BASE_URL}/${MODULE_IMS_URL}/Item`,             //刪除庫存品 (DELETE)
    generateTestItems: `${API_BASE_URL}/${MODULE_IMS_URL}/Item/GenerateTestData`, //產生測試資料 (POST) - 僅開發環境
    getItemTaxTypes: `${API_BASE_URL}/${MODULE_IMS_URL}/Item/GetItemTaxTypes`, //取得庫存品稅別選項 (GET)

    // ItemCategory 庫存品分類管理
    getItemCategoryList: `${API_BASE_URL}/${MODULE_IMS_URL}/ItemCategory`,            //取得庫存品分類列表
    getItemCategory: `${API_BASE_URL}/${MODULE_IMS_URL}/ItemCategory`,                //取得庫存品分類 (GET)
    addItemCategory: `${API_BASE_URL}/${MODULE_IMS_URL}/ItemCategory`,                //新增庫存品分類 (POST)
    editItemCategory: `${API_BASE_URL}/${MODULE_IMS_URL}/ItemCategory`,               //修改庫存品分類 (PUT)
    deleteItemCategory: `${API_BASE_URL}/${MODULE_IMS_URL}/ItemCategory`,             //刪除庫存品分類 (DELETE)
    sortItemCategory: `${API_BASE_URL}/${MODULE_IMS_URL}/ItemCategory/Sort`,          //批次排序庫存品分類 (POST)

    // ItemPrice 庫存品價格管理
    getItemPriceList: `${API_BASE_URL}/${MODULE_IMS_URL}/ItemPrice`,            //取得庫存品價格列表
    getItemPrice: `${API_BASE_URL}/${MODULE_IMS_URL}/ItemPrice`,                //取得庫存品價格 (GET)
    addItemPrice: `${API_BASE_URL}/${MODULE_IMS_URL}/ItemPrice`,                //新增庫存品價格 (POST)
    editItemPrice: `${API_BASE_URL}/${MODULE_IMS_URL}/ItemPrice`,               //修改庫存品價格 (PUT)
    deleteItemPrice: `${API_BASE_URL}/${MODULE_IMS_URL}/ItemPrice`,             //刪除庫存品價格 (DELETE)

    // PriceType 價格類型管理
    getPriceTypeList: `${API_BASE_URL}/${MODULE_IMS_URL}/PriceType`,            //取得價格類型列表
    getPriceType: `${API_BASE_URL}/${MODULE_IMS_URL}/PriceType`,                //取得價格類型 (GET)
    addPriceType: `${API_BASE_URL}/${MODULE_IMS_URL}/PriceType`,                //新增價格類型 (POST)
    editPriceType: `${API_BASE_URL}/${MODULE_IMS_URL}/PriceType`,               //修改價格類型 (PUT)
    deletePriceType: `${API_BASE_URL}/${MODULE_IMS_URL}/PriceType`,             //刪除價格類型 (DELETE)
    sortPriceType: `${API_BASE_URL}/${MODULE_IMS_URL}/PriceType/Sort`,          //批次排序價格類型 (POST)

    // Partner 商業夥伴管理
    getPartnerList: `${API_BASE_URL}/${MODULE_IMS_URL}/Partner`,          //取得商業夥伴列表
    getPartner: `${API_BASE_URL}/${MODULE_IMS_URL}/Partner`,              //取得商業夥伴 (GET)
    addPartner: `${API_BASE_URL}/${MODULE_IMS_URL}/Partner`,              //新增商業夥伴 (POST)
    editPartner: `${API_BASE_URL}/${MODULE_IMS_URL}/Partner`,             //修改商業夥伴 (PUT)
    deletePartner: `${API_BASE_URL}/${MODULE_IMS_URL}/Partner`,           //刪除商業夥伴 (DELETE)

    // CustomerCategory 客戶分類管理
    getCustomerCategoryList: `${API_BASE_URL}/${MODULE_IMS_URL}/CustomerCategory`,            //取得客戶分類列表
    getCustomerCategory: `${API_BASE_URL}/${MODULE_IMS_URL}/CustomerCategory`,                //取得客戶分類
    addCustomerCategory: `${API_BASE_URL}/${MODULE_IMS_URL}/CustomerCategory`,                //新增客戶分類
    editCustomerCategory: `${API_BASE_URL}/${MODULE_IMS_URL}/CustomerCategory`,               //修改客戶分類
    deleteCustomerCategory: `${API_BASE_URL}/${MODULE_IMS_URL}/CustomerCategory`,             //刪除客戶分類
    sortCustomerCategory: `${API_BASE_URL}/${MODULE_IMS_URL}/CustomerCategory/Sort`,          //批次排序客戶分類 (POST)

    // SupplierCategory 供應商分類管理
    getSupplierCategoryList: `${API_BASE_URL}/${MODULE_IMS_URL}/SupplierCategory`,            //取得供應商分類列表
    getSupplierCategory: `${API_BASE_URL}/${MODULE_IMS_URL}/SupplierCategory`,                //取得供應商分類
    addSupplierCategory: `${API_BASE_URL}/${MODULE_IMS_URL}/SupplierCategory`,                //新增供應商分類
    editSupplierCategory: `${API_BASE_URL}/${MODULE_IMS_URL}/SupplierCategory`,               //修改供應商分類
    deleteSupplierCategory: `${API_BASE_URL}/${MODULE_IMS_URL}/SupplierCategory`,              //刪除供應商分類
    sortSupplierCategory: `${API_BASE_URL}/${MODULE_IMS_URL}/SupplierCategory/Sort`,          //批次排序供應商分類 (POST)

    // Contact 聯絡人管理
    getContactList: `${API_BASE_URL}/${MODULE_IMS_URL}/Contact`,            //取得聯絡人列表
    getContact: `${API_BASE_URL}/${MODULE_IMS_URL}/Contact`,                //取得聯絡人 (GET)
    addContact: `${API_BASE_URL}/${MODULE_IMS_URL}/Contact`,                //新增聯絡人 (POST)
    editContact: `${API_BASE_URL}/${MODULE_IMS_URL}/Contact`,               //修改聯絡人 (PUT)
    deleteContact: `${API_BASE_URL}/${MODULE_IMS_URL}/Contact`,             //刪除聯絡人 (DELETE)
    searchContacts: `${API_BASE_URL}/${MODULE_IMS_URL}/Contact`,            //搜尋聯絡人 (GET with query)

    // PartnerAddress 商業夥伴地址管理
    getPartnerAddressList: `${API_BASE_URL}/${MODULE_IMS_URL}/PartnerAddress`,            //取得夥伴地址列表
    getPartnerAddress: `${API_BASE_URL}/${MODULE_IMS_URL}/PartnerAddress`,                //取得夥伴地址 (GET)
    addPartnerAddress: `${API_BASE_URL}/${MODULE_IMS_URL}/PartnerAddress`,                //新增夥伴地址 (POST)
    editPartnerAddress: `${API_BASE_URL}/${MODULE_IMS_URL}/PartnerAddress`,               //修改夥伴地址 (PUT)
    deletePartnerAddress: `${API_BASE_URL}/${MODULE_IMS_URL}/PartnerAddress`,             //刪除夥伴地址 (DELETE)

    // PartnerContact 商業夥伴聯絡人關聯管理
    getPartnerContactList: `${API_BASE_URL}/${MODULE_IMS_URL}/PartnerContact`,            //取得夥伴聯絡人關聯列表
    getPartnerContactByPartner: `${API_BASE_URL}/${MODULE_IMS_URL}/PartnerContact/partner`, //取得夥伴的所有聯絡人
    getPartnerContact: `${API_BASE_URL}/${MODULE_IMS_URL}/PartnerContact`,                //取得特定聯絡人關聯
    addPartnerContact: `${API_BASE_URL}/${MODULE_IMS_URL}/PartnerContact`,                //關聯聯絡人到夥伴
    editPartnerContact: `${API_BASE_URL}/${MODULE_IMS_URL}/PartnerContact`,               //更新聯絡人關聯
    deletePartnerContact: `${API_BASE_URL}/${MODULE_IMS_URL}/PartnerContact`,             //取消聯絡人關聯
    addContactAndAssociate: `${API_BASE_URL}/${MODULE_IMS_URL}/PartnerContact/add-and-associate`, //新增聯絡人並關聯
    // #endregion

    // ReportSignatureTemplate 簽核範本管理
    getReportSignatureTemplates: `${API_BASE_URL}/${MODULE_COMMON_URL}/ReportSignatureTemplate/GetAll`,
    getReportSignatureTemplateById: `${API_BASE_URL}/${MODULE_COMMON_URL}/ReportSignatureTemplate/Get`,
    addReportSignatureTemplate: `${API_BASE_URL}/${MODULE_COMMON_URL}/ReportSignatureTemplate/Create`,
    updateReportSignatureTemplate: `${API_BASE_URL}/${MODULE_COMMON_URL}/ReportSignatureTemplate/Edit`,
    deleteReportSignatureTemplate: `${API_BASE_URL}/${MODULE_COMMON_URL}/ReportSignatureTemplate/Delete`,

    // Logger 系統日誌查詢
    getLoggerLogs: `${API_BASE_URL}/${MODULE_COMMON_URL}/Logger/logs`,
    getLoggerSources: `${API_BASE_URL}/${MODULE_COMMON_URL}/Logger/sources`,

    // GranularPermission 細粒度權限管理
    granularMyPermissions: `${API_BASE_URL}/${MODULE_COMMON_URL}/GranularPermission/me`,
    granularCheckPermission: `${API_BASE_URL}/${MODULE_COMMON_URL}/GranularPermission/Check`,
    granularGetPermissions: `${API_BASE_URL}/${MODULE_COMMON_URL}/GranularPermission/resources`,
    granularGetPermissionActions: (permissionId: string) => `${API_BASE_URL}/${MODULE_COMMON_URL}/GranularPermission/resources/${permissionId}/actions`,
    granularGetAllActions: `${API_BASE_URL}/${MODULE_COMMON_URL}/GranularPermission/actions`,
    // 批次取得多筆權限的動作（以 query string 傳遞多個 permissionId）
    granularGetActionsBatch: (permissionIdsCsv: string) => `${API_BASE_URL}/${MODULE_COMMON_URL}/GranularPermission/actions/batch?permissionIds=${permissionIdsCsv}`,
    granularGetValidActions: `${API_BASE_URL}/${MODULE_COMMON_URL}/GranularPermission/actions/valid`,
    granularCreatePermission: `${API_BASE_URL}/${MODULE_COMMON_URL}/GranularPermission/resources`,
    granularUpdatePermission: `${API_BASE_URL}/${MODULE_COMMON_URL}/GranularPermission/resources`,
    granularCreateAction: `${API_BASE_URL}/${MODULE_COMMON_URL}/GranularPermission/actions`,
    granularUpdateAction: `${API_BASE_URL}/${MODULE_COMMON_URL}/GranularPermission/actions`,
    granularBatchCreateActions: `${API_BASE_URL}/${MODULE_COMMON_URL}/GranularPermission/actions/batch`,
    granularBatchUpdateActions: `${API_BASE_URL}/${MODULE_COMMON_URL}/GranularPermission/actions/batch`,
    granularGetRolePermissions: (roleId: string) => `${API_BASE_URL}/${MODULE_COMMON_URL}/GranularPermission/roles/${roleId}`,
    granularBatchUpdate: `${API_BASE_URL}/${MODULE_COMMON_URL}/GranularPermission/roles/batch`,

    /**
     * SMS 伺服器管理系統
     * **/
    // SmsDbServer 資料庫伺服器管理
    getSmsDbServers: `${API_BASE_URL}/${MODULE_SMS_URL}/SmsDbServers`,
    getSmsDbServerById: `${API_BASE_URL}/${MODULE_SMS_URL}/SmsDbServers`,
    addSmsDbServer: `${API_BASE_URL}/${MODULE_SMS_URL}/SmsDbServers`,
    editSmsDbServer: `${API_BASE_URL}/${MODULE_SMS_URL}/SmsDbServers`,
    deleteSmsDbServer: `${API_BASE_URL}/${MODULE_SMS_URL}/SmsDbServers`,
    getSmsDbServerOptions: `${API_BASE_URL}/${MODULE_SMS_URL}/SmsDbServers/options`,
    getSmsDbServerOperatingSystems: `${API_BASE_URL}/${MODULE_SMS_URL}/SmsDbServers/operating-systems`,
    getSmsDbServerEnvironments: `${API_BASE_URL}/${MODULE_SMS_URL}/SmsDbServers/environments`,
    getSmsDbServerDatabases: `${API_BASE_URL}/${MODULE_SMS_URL}/SmsDbServers/databases`,
    validateSmsDbServerName: `${API_BASE_URL}/${MODULE_SMS_URL}/SmsDbServers/validate-name`,

    // SmsWebServer 網頁伺服器管理
    getSmsWebServers: `${API_BASE_URL}/${MODULE_SMS_URL}/SmsWebServers`,
    getSmsWebServerById: `${API_BASE_URL}/${MODULE_SMS_URL}/SmsWebServers`,
    addSmsWebServer: `${API_BASE_URL}/${MODULE_SMS_URL}/SmsWebServers`,
    editSmsWebServer: `${API_BASE_URL}/${MODULE_SMS_URL}/SmsWebServers`,
    deleteSmsWebServer: `${API_BASE_URL}/${MODULE_SMS_URL}/SmsWebServers`,
    getSmsWebServerOptions: `${API_BASE_URL}/${MODULE_SMS_URL}/SmsWebServers/options`,
    getSmsWebServerOperatingSystems: `${API_BASE_URL}/${MODULE_SMS_URL}/SmsWebServers/operating-systems`,
    getSmsWebServerEnvironments: `${API_BASE_URL}/${MODULE_SMS_URL}/SmsWebServers/environments`,

    // SmsSite 站台管理
    getSmsSites: `${API_BASE_URL}/${MODULE_SMS_URL}/SmsSites`,
    getSmsSiteById: `${API_BASE_URL}/${MODULE_SMS_URL}/SmsSites`,
    addSmsSite: `${API_BASE_URL}/${MODULE_SMS_URL}/SmsSites`,
    editSmsSite: `${API_BASE_URL}/${MODULE_SMS_URL}/SmsSites`,
    deleteSmsSite: `${API_BASE_URL}/${MODULE_SMS_URL}/SmsSites`,
    validateSmsSiteName: `${API_BASE_URL}/${MODULE_SMS_URL}/SmsSites/validate-name`,
    getSmsSiteOptions: `${API_BASE_URL}/${MODULE_SMS_URL}/SmsSites/options`,
    getSmsSiteStatistics: `${API_BASE_URL}/${MODULE_SMS_URL}/SmsSites/statistics`,
    getSmsSiteEnvironmentDistribution: `${API_BASE_URL}/${MODULE_SMS_URL}/SmsSites/environment-distribution`,
    getRecentSmsSites: `${API_BASE_URL}/${MODULE_SMS_URL}/SmsSites/recent`,
    getRecentlyCreatedSmsSites: `${API_BASE_URL}/${MODULE_SMS_URL}/SmsSites/recently-created`,
    exportSmsSiteReport: `${API_BASE_URL}/${MODULE_SMS_URL}/SmsSites/export-report`,
} as const;

// API 請求配置
export const apiConfig = {
    baseURL: API_BASE_URL,
    // 不設定 header，讓 axios 自動決定
} as const;

// API 響應介面
export interface ApiResponse<T = any> {
    success: boolean;
    message?: string;
    data?: T;
}