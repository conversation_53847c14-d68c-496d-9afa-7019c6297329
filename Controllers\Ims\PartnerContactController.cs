using FAST_ERP_Backend.Interfaces.Ims;
using FAST_ERP_Backend.Models.Ims;
using FAST_ERP_Backend.Models;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using FAST_ERP_Backend.Filters;

namespace FAST_ERP_Backend.Controllers.Ims;

/// <summary> 商業夥伴聯絡人管理 </summary>
[ApiController]
[Route("api/[controller]")]
[SwaggerTag("商業夥伴聯絡人管理")]
public class PartnerContactController(IPartnerContactService _partnerContactService) : ControllerBase
{
    /// <summary> 取得商業夥伴的所有聯絡人 </summary>
    [HttpGet("partner/{partnerId}")]
    [RequirePermission("Ims/PartnerContact", "Read")]
    [SwaggerOperation(Summary = "取得商業夥伴的所有聯絡人", Description = "取得指定商業夥伴的所有聯絡人關聯")]
    public async Task<IActionResult> GetByPartner(Guid partnerId)
    {
        try
        {
            if (partnerId == Guid.Empty)
            {
                return BadRequest(ApiResponse<object>.ErrorResult("商業夥伴ID不能為空", 400));
            }

            var contacts = await _partnerContactService.GetAsync(partnerId);
            return Ok(ApiResponse<List<PartnerContactDTO>>.SuccessResult(contacts, "取得商業夥伴聯絡人列表成功"));
        }
        catch (Exception ex)
        {
            return BadRequest(ApiResponse<object>.ErrorResult($"取得聯絡人列表失敗: {ex.Message}", 400));
        }
    }

    /// <summary> 取得特定聯絡人關聯 </summary>
    [HttpGet("{partnerContactId}")]
    [RequirePermission("Ims/PartnerContact", "Read")]
    [SwaggerOperation(Summary = "取得特定聯絡人關聯", Description = "根據關聯ID取得聯絡人關聯詳情")]
    public async Task<IActionResult> Get(Guid partnerContactId)
    {
        try
        {
            if (partnerContactId == Guid.Empty)
            {
                return BadRequest(ApiResponse<object>.ErrorResult("聯絡人關聯ID不能為空", 400));
            }

            var contact = await _partnerContactService.GetByIdAsync(partnerContactId);
            if (contact == null)
            {
                return NotFound(ApiResponse<object>.ErrorResult("找不到指定的聯絡人關聯", 404));
            }

            return Ok(ApiResponse<PartnerContactDTO>.SuccessResult(contact, "取得聯絡人關聯成功"));
        }
        catch (Exception ex)
        {
            return BadRequest(ApiResponse<object>.ErrorResult($"取得聯絡人關聯失敗: {ex.Message}", 400));
        }
    }

    /// <summary> 關聯聯絡人到夥伴 </summary>
    [HttpPost]
    [RequirePermission("Ims/PartnerContact", "Create")]
    [SwaggerOperation(Summary = "關聯聯絡人到夥伴", Description = "將現有聯絡人關聯到指定夥伴")]
    public async Task<IActionResult> Add([FromBody] AssociateContactRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values
                    .SelectMany(v => v.Errors)
                    .Select(e => e.ErrorMessage)
                    .ToList();
                return BadRequest(ApiResponse<object>.ErrorResult($"資料驗證失敗: {string.Join(", ", errors)}", 400));
            }

            var (result, message) = await _partnerContactService.AssociateAsync(request);

            if (result)
            {
                return Ok(ApiResponse<object>.SuccessResult(new object(), message));
            }
            else
            {
                return BadRequest(ApiResponse<object>.ErrorResult(message, 400));
            }
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ApiResponse<object>.ErrorResult(ex.Message, 400));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<object>.ErrorResult($"關聯聯絡人失敗: {ex.Message}", 500));
        }
    }

    /// <summary> 更新聯絡人關聯 </summary>
    [HttpPut]
    [RequirePermission("Ims/PartnerContact", "Update")]
    [SwaggerOperation(Summary = "更新聯絡人關聯", Description = "更新聯絡人與夥伴的關聯資訊")]
    public async Task<IActionResult> Update([FromBody] UpdateContactAssociationRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values
                    .SelectMany(v => v.Errors)
                    .Select(e => e.ErrorMessage)
                    .ToList();
                return BadRequest(ApiResponse<object>.ErrorResult($"資料驗證失敗: {string.Join(", ", errors)}", 400));
            }

            var (result, message) = await _partnerContactService.UpdateAssociationAsync(request);

            if (result)
            {
                return Ok(ApiResponse<object>.SuccessResult(new object(), message));
            }
            else
            {
                return BadRequest(ApiResponse<object>.ErrorResult(message, 400));
            }
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ApiResponse<object>.ErrorResult(ex.Message, 400));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<object>.ErrorResult($"更新聯絡人關聯失敗: {ex.Message}", 500));
        }
    }

    /// <summary> 取消聯絡人關聯 </summary>
    [HttpDelete]
    [RequirePermission("Ims/PartnerContact", "Delete")]
    [SwaggerOperation(Summary = "取消聯絡人關聯", Description = "移除聯絡人與夥伴的關聯")]
    public async Task<IActionResult> Delete(Guid partnerContactId)
    {
        try
        {
            if (partnerContactId == Guid.Empty)
            {
                return BadRequest(ApiResponse<object>.ErrorResult("聯絡人關聯ID不能為空", 400));
            }

            var (success, message) = await _partnerContactService.UnassociateAsync(partnerContactId);
            if (!success)
            {
                return BadRequest(ApiResponse<object>.ErrorResult(message, 400));
            }

            return Ok(ApiResponse<object>.SuccessResult(new object(), message));
        }
        catch (Exception ex)
        {
            return BadRequest(ApiResponse<object>.ErrorResult($"取消聯絡人關聯失敗: {ex.Message}", 400));
        }
    }

    /// <summary> 新增聯絡人並關聯 </summary>
    [HttpPost("add-and-associate")]
    [RequirePermission("Ims/PartnerContact", "Create")]
    [SwaggerOperation(Summary = "新增聯絡人並關聯", Description = "一次性新增聯絡人並建立夥伴關聯")]
    public async Task<IActionResult> AddAndAssociate([FromBody] AddContactAndAssociateRequest request)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                var errors = ModelState.Values
                    .SelectMany(v => v.Errors)
                    .Select(e => e.ErrorMessage)
                    .ToList();
                return BadRequest(ApiResponse<object>.ErrorResult($"資料驗證失敗: {string.Join(", ", errors)}", 400));
            }

            var (result, message) = await _partnerContactService.AddContactAndAssociateAsync(request);

            if (result)
            {
                return Ok(ApiResponse<object>.SuccessResult(new object(), message));
            }
            else
            {
                return BadRequest(ApiResponse<object>.ErrorResult(message, 400));
            }
        }
        catch (ArgumentException ex)
        {
            return BadRequest(ApiResponse<object>.ErrorResult(ex.Message, 400));
        }
        catch (Exception ex)
        {
            return StatusCode(500, ApiResponse<object>.ErrorResult($"新增聯絡人並關聯失敗: {ex.Message}", 500));
        }
    }
} 