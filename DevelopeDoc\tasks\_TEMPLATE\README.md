# 任務文件範本（勿直接修改此檔）

## 任務基本資訊
- 任務代碼：<task-id>
- 任務標題：<title>
- 所屬模組：<module(s)>
- 需求提出：<owner/pm>
- 相關 issue / PR：<links>

## 目錄
- specs/：需求與業務規格
- apis/：端點、型別、錯誤碼
- decisions/：ADR 決策
- examples/：檢查清單和範例
- plan.md：實作規劃、里程碑、驗收
- status.md：進度、阻塞、依賴
- **analysis/：架構分析和評估**
- **improvement/：改進計畫和追蹤**
- **testing/：API 測試和驗證**

## 使用方式
1. 複製 `_TEMPLATE` 為新資料夾：`<task-id>-<slug>`
2. 依序完成 specs/、apis/、plan.md 與 decisions/
3. 開發過程持續更新 `status.md`

## 📚 專案架構參考

> **詳細架構指南請參考**：`examples/architecture_reference.md`

### 核心文件
- **`DEVELOPMENT_GUIDELINES.md`**：後端開發準則
- **`ModuleRouteConvention.cs`**：路由自動處理機制  
- **`api.ts`**：前端 API 端點配置

### 快速檢查
- [ ] 已閱讀架構參考文件
- [ ] 確認前後端路由一致性

## ⚠️ 開發流程提醒

### 必須遵循的開發順序
1. **需求分析階段**
   - [ ] 分析使用者案例和使用情境
   - [ ] 檢查現有功能完整性
   - [ ] 確認後端 API 實作狀況
   - [ ] 分析資料庫結構設計
   - [ ] 確認權限需求設計

2. **規格設計階段**
   - [ ] 完成 specs/ 目錄下的規格文件
   - [ ] 定義清楚的使用者故事
   - [ ] 確認業務規則和驗證邏輯
   - [ ] 設計資料結構和 API 介面

3. **實作規劃階段**
   - [ ] 完成 plan.md 實作規劃
   - [ ] 切分任務和里程碑
   - [ ] 定義驗收條件
   - [ ] 識別風險和緩解方案

4. **開發實作階段**
   - [ ] 先實作後端 API（如需要）
   - [ ] 再實作前端功能
   - [ ] 遵循開發準則和命名規範
   - [ ] 確保資料庫事務一致性

### 禁止的開發方式
- ❌ 沒有分析現有功能就開始修改
- ❌ 沒有確認後端 API 就開始前端開發
- ❌ 沒有任務管理就開始實作
- ❌ 沒有使用者案例分析就設計功能
- ❌ 沒有權限分析就實作功能

### 檢查清單
> **詳細檢查清單請參考**：`examples/unified_checklist.md`

- [ ] 已分析現有功能和需求
- [ ] 已確認後端 API 實作狀況
- [ ] 已建立完整的任務清單
- [ ] 已遵循開發準則

