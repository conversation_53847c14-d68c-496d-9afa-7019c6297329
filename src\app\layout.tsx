"use client";

/*全域佈局
/app/components/layout/Layout.tsx
*/

import { Inter } from "next/font/google";
import StyledComponentsRegistry from "@/lib/AntdRegistry";
import { ConfigProvider } from "antd";
import zhTW from "antd/locale/zh_TW";
import dayjs from "dayjs";
import "dayjs/locale/zh-tw";
import MainLayout from "@/app/components/layout/MainLayout";
import { AuthProvider } from "@/contexts/AuthContext";
import { GranularPermissionProvider } from "@/contexts/GranularPermissionContext";
import { ContactProvider } from "@/contexts/ContactContext";
import { usePathname } from "next/navigation";
import { useEffect, useState } from "react";
import "./globals.css";
import { OptionsProvider } from "@/contexts/OptionsContext";
import { eventBus } from "@/utils/eventBus";
import { createContextLogger, type LogContext, SYMBOLS } from "@/utils/logger";

const inter = Inter({ subsets: ["latin"] });

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname();
  const isLoginPage = pathname === "/login";
  const [isMobile, setIsMobile] = useState(false);
  const logger = createContextLogger({ module: 'Layout', component: 'RootLayout' });

  useEffect(() => {
    // 設定 dayjs 全域語系為繁體中文
    dayjs.locale("zh-tw");
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  // 開發模式下，將模擬事件橋接至 window，便於 E2E 測試觸發 granular-permissions.refresh
  if (process.env.NODE_ENV === 'development' && typeof window !== 'undefined') {
    (window as any).__emitPermissionsUpdated = (data?: any) => {
      logger.log(SYMBOLS.INFO, '開發模式：觸發權限測試事件', { data });
      eventBus.emit('granular-permissions', { action: 'refresh', data });
    };
  }

  return (
    <html lang="zh-TW">
      <head>
        <meta
          name="viewport"
          content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no"
        />
      </head>
      <body
        className={inter.className}
        style={{
          margin: 0,
          padding: 0,
          height: "100vh",
          background: "#f5f5f5",
          overflowX: "hidden",
          WebkitOverflowScrolling: "touch",
          fontSize: isMobile ? "14px" : "16px",
        }}
      >
        <StyledComponentsRegistry>
          <ConfigProvider locale={zhTW} theme={{ token: {} }}>
            <AuthProvider>
              <OptionsProvider>
                <ContactProvider>
                  {isLoginPage ? (
                    children
                  ) : (
                    <GranularPermissionProvider>
                      <MainLayout>{children}</MainLayout>
                    </GranularPermissionProvider>
                  )}
                </ContactProvider>
              </OptionsProvider>
            </AuthProvider>
          </ConfigProvider>
        </StyledComponentsRegistry>
      </body>
    </html>
  );
}
