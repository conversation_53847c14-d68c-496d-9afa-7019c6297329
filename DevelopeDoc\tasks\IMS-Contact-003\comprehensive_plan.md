# IMS-Contact-003 綜合實作規劃（調整版）

## 任務範圍調整總結

### 基於用戶修正的調整
根據用戶提供的修正和澄清，任務範圍已從大規模架構重構調整為重點處理：

1. **API 回應格式一致性問題** - 處理不同端點間的回應格式差異
2. **前後端驗證規則同步** - 確保驗證邏輯的一致性
3. **IMS 控制器架構狀態驗證** - 確認所有控制器的實際架構狀態

### 確認的架構狀態
- ✅ **PartnerController** - 已使用泛型架構（用戶確認）
- ✅ **ContactController** - 已使用泛型架構
- ✅ **前端路由** - `http://localhost:3000/ims/basic/partner` 正常運作
- ✅ **API 測試** - TestLogin 端點正常回應

## 修正後的實作規劃

### 階段一：架構狀態最終驗證（預計 0.5 天）

#### 1.1 IMS 控制器架構驗證
- [ ] **驗證 PartnerController 實際架構**
  - 確認是否真的使用 GenericController<T>
  - 檢查 API 回應格式
  - 驗證 CRUD 操作實作

- [ ] **驗證 ItemController 架構狀態**
  - 檢查控制器繼承關係
  - 分析程式碼結構和行數
  - 確認是否需要架構遷移

- [ ] **驗證 PartnerAddressController 架構狀態**
  - 檢查控制器繼承關係
  - 分析程式碼結構和行數
  - 確認是否需要架構遷移

#### 1.2 API 回應格式分析
- [ ] **分析 GenericController 回應格式**
  ```json
  {
    "success": true,
    "message": "取得{實體名稱}列表成功",
    "data": [...],
    "paginate": null
  }
  ```

- [ ] **分析傳統 Controller 回應格式**
  ```json
  {
    "success": true,
    "data": [...],
    "message": "取得成功"
  }
  ```

- [ ] **制定統一格式標準**
  - 使用 ApiResponse<T>.SuccessResult() 和 ErrorResult()
  - 確保屬性順序一致性
  - 統一錯誤處理格式

### 階段二：API 一致性修正（預計 1 天）

#### 2.1 API 回應格式統一
- [ ] **修正 PartnerController 回應格式**（如需要）
  - 統一使用 ApiResponse<T> 格式
  - 移除手動建構的匿名物件回應
  - 確保屬性順序一致

- [ ] **修正 ItemController 回應格式**（如需要）
  - 統一使用 ApiResponse<T> 格式
  - 移除手動建構的匿名物件回應
  - 確保屬性順序一致

- [ ] **修正 PartnerAddressController 回應格式**（如需要）
  - 統一使用 ApiResponse<T> 格式
  - 移除手動建構的匿名物件回應
  - 確保屬性順序一致

#### 2.2 架構遷移（如需要）
- [ ] **ItemController 泛型架構遷移**（如需要）
  - 繼承 GenericController<Item, ItemDTO>
  - 移除重複的 CRUD 實作
  - 保留特殊業務邏輯

- [ ] **PartnerAddressController 泛型架構遷移**（如需要）
  - 繼承 GenericController<PartnerAddress, PartnerAddressDTO>
  - 移除重複的 CRUD 實作
  - 保留特殊業務邏輯

### 階段三：前後端驗證規則同步（預計 1 天）

#### 3.1 Contact 驗證規則同步
- [ ] **後端驗證規則分析**
  - 檢查 Contact.cs 中的 [Required] 屬性
  - 檢查 ContactDTO.cs 中的驗證屬性
  - 整理必填欄位清單

- [ ] **前端驗證規則調整**
  - 更新 contactValidation.ts
  - 移除不合理的必填限制（優先序、角色）
  - 統一錯誤訊息格式

- [ ] **驗證規則測試**
  - 更新測試案例
  - 確保前後端一致性
  - 驗證錯誤訊息格式

#### 3.2 其他實體驗證規則檢查
- [ ] **Partner 驗證規則檢查**
  - 比較前後端驗證邏輯
  - 識別不一致問題
  - 制定修正方案

- [ ] **Item 驗證規則檢查**
  - 比較前後端驗證邏輯
  - 識別不一致問題
  - 制定修正方案

### 階段四：API 測試和驗證（預計 0.5 天）

#### 4.1 API 測試執行
- [ ] **使用 curl.exe 測試所有 IMS API**
  ```bash
  # 獲取 Token
  curl.exe -X POST "https://localhost:7137/api/common/Login/TestLogin?acc=FastAdmin&pw=fast%21234" -k

  # 測試 Partner API
  curl.exe -X GET "https://localhost:7137/api/ims/Partner" -k -H "Authorization: [token]"

  # 測試 Contact API
  curl.exe -X GET "https://localhost:7137/api/ims/Contact" -k -H "Authorization: [token]"

  # 測試 Item API
  curl.exe -X GET "https://localhost:7137/api/ims/Item" -k -H "Authorization: [token]"
  ```

- [ ] **驗證回應格式一致性**
  - 檢查所有 API 回應格式
  - 確認屬性順序一致
  - 驗證錯誤處理格式

#### 4.2 前端整合測試
- [ ] **前端路由測試**
  - 測試 `http://localhost:3000/ims/basic/partner`
  - 確保頁面完全載入
  - 驗證功能正常運作

- [ ] **前後端整合測試**
  - 測試表單提交流程
  - 驗證資料驗證一致性
  - 確認錯誤訊息顯示

## 里程碑和時程

| 里程碑 | 預計完成時間 | 驗收條件 | 依賴關係 |
|--------|-------------|----------|----------|
| M1 架構狀態驗證完成 | 0.5 天 | 所有 IMS 控制器架構狀態明確 | - |
| M2 API 一致性修正完成 | 1.5 天 | 所有 API 回應格式統一 | M1 |
| M3 驗證規則同步完成 | 2.5 天 | 前後端驗證規則完全一致 | M2 |
| M4 測試驗證完成 | 3 天 | 所有測試通過，功能正常 | M3 |

## 預期效益（修正版）

### 工作量大幅減少
- **原估計：** 3個控制器需要遷移（~558 行 → ~105 行）
- **修正後：** 可能只需要 0-2 個控制器遷移
- **減少幅度：** 80-90%

### 主要改進效益
- **API 一致性：** 顯著提升
- **開發效率：** 提升 30-50%
- **維護複雜度：** 大幅降低
- **使用者體驗：** 驗證規則一致性改善

## 風險控制

### 主要風險
1. **控制器架構狀態不明確** - 可能影響工作量估算
2. **API 格式修改影響前端** - 需要協調前後端修改
3. **驗證規則修改影響使用者** - 需要充分測試

### 緩解策略
- 先進行完整的架構狀態驗證
- 分階段進行 API 格式修正
- 充分的回歸測試和使用者驗收測試

## 成功標準

### 技術標準
- [ ] 所有 IMS API 回應格式統一使用 ApiResponse<T>
- [ ] 前後端驗證規則完全同步
- [ ] 所有 API 測試通過
- [ ] 前端功能正常運作

### 品質標準
- [ ] 程式碼品質提升
- [ ] 維護複雜度降低
- [ ] 開發效率提升
- [ ] 使用者體驗改善
