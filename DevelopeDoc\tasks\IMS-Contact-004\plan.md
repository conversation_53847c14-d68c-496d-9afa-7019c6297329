# IMS-Contact-004 實作規劃

## 任務概述
Contact 組件標準化重構，確保完全符合 IMS 標準和最佳實踐，參考 Item 模組設計模式。

**🔄 任務整合更新**：
本計劃已整合 IMS-Contact-003 的剩餘工作項目（表單欄位統一、資料流轉優化、測試驗證），形成增強版實施計劃。通過合併執行，避免重複工作，提升整體效益。

## 實施階段

### 階段一：組件標準化重構 + 表單統一（3天）

#### 1.1 ContactFormModal 重構 + 表單欄位統一（1.5天）

**目標**：統一表單設計，支援多種模式，使用 ResponsiveModalConfig，解決表單欄位不一致問題（整合 IMS-Contact-003 階段三）

**實作項目**：
- [ ] **引入 ResponsiveModalConfig**
  - 使用 `getResponsiveModalConfig(screenSize)` 函數
  - 統一響應式設計配置
  - 支援移動端、平板、桌面三種斷點

- [ ] **重構表單模式支援**
  - `create` 模式：完整新增（8個基本欄位）
  - `quick` 模式：快速新增（4個基本欄位 + 5個 PartnerContact 欄位）
  - `edit` 模式：編輯（根據情境顯示相應欄位）

- [ ] **統一欄位設計（解決 IMS-Contact-003 表單欄位不一致問題）**
  - 基礎欄位：姓名*、電子郵件、聯絡人類型、狀態
  - 詳細欄位：職位、電話、部門、公司（**新增到快速模式**）
  - 關聯欄位：角色*、優先序*、專業領域、備註、業務範圍
  - **解決問題**：快速新增表單增加職位、電話、部門、公司欄位，與完整新增保持一致

- [ ] **參考 ItemFormModal 設計模式**
  - 使用 Card 組件分組欄位
  - 統一的表單驗證邏輯
  - 一致的按鈕佈局和樣式

**驗收條件**：
- ✅ 支援三種表單模式
- ✅ 響應式設計正常工作
- ✅ 表單驗證規則一致
- ✅ 視覺設計符合 Item 模組標準
- ✅ **表單欄位統一**：快速新增包含完整的 8 個基本欄位
- ✅ **資料流轉正常**：快速新增資料能完整帶入編輯表單

#### 1.2 篩選器統一重構（1天）

**目標**：使用 FilterSearchContainer 統一篩選功能

**實作項目**：
- [ ] **定義 contactFilterOptions**
  ```typescript
  const contactFilterOptions: FilterOption[] = [
    CommonFilterOptions.textInput("聯絡人姓名", "name", "輸入姓名進行搜尋"),
    CommonFilterOptions.textInput("電子郵件", "email", "輸入電子郵件進行搜尋"),
    CommonFilterOptions.textInput("公司", "company", "輸入公司名稱進行搜尋"),
    CommonFilterOptions.textInput("部門", "department", "輸入部門名稱進行搜尋"),
    CommonFilterOptions.statusSelect("聯絡人類型", "contactType", CONTACT_TYPES),
    CommonFilterOptions.statusSelect("狀態", "isActive", [
      { label: "啟用", value: "true" },
      { label: "停用", value: "false" }
    ])
  ];
  ```

- [ ] **實作 applyContactFilters 函數**
  - 參考 Item 模組的 applyItemFilters 設計
  - 支援搜尋文字篩選
  - 支援動態篩選條件

- [ ] **替換現有篩選組件**
  - 移除 ContactFilters.tsx
  - 移除 ContactSearchForm.tsx
  - 使用 FilterSearchContainer 統一管理

**驗收條件**：
- ✅ 篩選功能完全正常
- ✅ 搜尋功能正常工作
- ✅ 篩選結果統計正確
- ✅ 響應式設計適配

#### 1.3 ContactTable 優化（0.5天）

**目標**：確保 ContactTable 完全符合 ResponsiveTable 最佳實踐

**實作項目**：
- [ ] **檢查 ResponsiveTable 使用**
  - 確認所有功能正常
  - 優化行動端卡片顯示
  - 統一排序和篩選功能

- [ ] **智能篩選器整合**
  - 使用 SmartColumnType 定義欄位
  - 啟用欄位級智能篩選器
  - 統一篩選器樣式

**驗收條件**：
- ✅ 表格響應式設計正常
- ✅ 智能篩選器正常工作
- ✅ 行動端卡片顯示正確

### 階段二：操作流程優化（1-2天）

#### 2.1 Partner 頁面聯絡人管理優化（1天）

**目標**：統一 Partner 頁面的聯絡人管理流程

**實作項目**：
- [ ] **統一新增聯絡人入口**
  - Partner 頁面使用相同的 ContactFormModal
  - 傳入 `mode="quick"` 參數
  - 自動處理 PartnerContact 關聯

- [ ] **優化快速新增流程**
  - 保持快速性：預設顯示4個基本欄位
  - 增加完整性：提供展開選項顯示詳細欄位
  - 自動填入 PartnerContact 相關欄位

- [ ] **改善關聯管理**
  - 簡化 PartnerContact 的建立流程
  - 優化編輯和刪除操作
  - 統一關聯狀態顯示

**驗收條件**：
- ✅ Partner 頁面聯絡人管理流程順暢
- ✅ 快速新增功能保持高效
- ✅ 關聯管理操作直觀

#### 2.2 主頁面聯絡人管理優化（1天）

**目標**：優化主頁面的聯絡人管理功能

**實作項目**：
- [ ] **整合 FilterSearchContainer**
  - 替換現有的篩選和搜尋組件
  - 使用統一的篩選選項配置
  - 優化篩選結果顯示

- [ ] **優化新增聯絡人流程**
  - 使用重構後的 ContactFormModal
  - 傳入 `mode="create"` 參數
  - 統一成功和錯誤處理

**驗收條件**：
- ✅ 主頁面篩選功能正常
- ✅ 新增聯絡人流程順暢
- ✅ 與 Partner 頁面操作一致

### 階段三：共享組件提取 + 整合測試（1天）

#### 3.1 組件移至 shared 目錄（0.5天）

**目標**：將優化後的組件移至共享目錄

**實作項目**：
- [ ] **移動組件檔案**
  - ContactFormModal → src/app/ims/components/shared/ContactFormModal.tsx
  - ContactTable → src/app/ims/components/shared/ContactTable.tsx
  - contactUtils → src/app/ims/components/shared/contactUtils.ts

- [ ] **更新匯入路徑**
  - 更新所有使用這些組件的檔案
  - 確保匯入路徑正確
  - 檢查無遺漏的參考

**驗收條件**：
- ✅ 所有組件成功移至 shared 目錄
- ✅ 匯入路徑更新正確
- ✅ 無建置錯誤

#### 3.2 文檔更新（0.5天）

**目標**：更新使用指南和範例

**實作項目**：
- [ ] **更新 USAGE_GUIDE.md**
  - 新增 Contact 組件使用範例
  - 說明三種表單模式的使用方式
  - 提供篩選器配置範例

- [ ] **建立 Contact 組件文檔**
  - ContactFormModal 使用說明
  - ContactTable 配置選項
  - 最佳實踐建議

**驗收條件**：
- ✅ 文檔內容完整準確
- ✅ 範例代碼可正常運行
- ✅ 使用說明清晰易懂

#### 3.3 整合測試與驗證（整合 IMS-Contact-003 階段五）

**目標**：完整測試所有功能，確保整合成功

**實作項目**：
- [ ] **表單功能測試**
  - 測試三種表單模式功能完整性
  - 驗證表單欄位統一性（快速新增包含 8 個基本欄位）
  - 測試表單驗證規則一致性

- [ ] **資料流轉測試**
  - 測試快速新增資料完整帶入編輯表單
  - 驗證預設值處理一致性
  - 測試欄位映射準確性

- [ ] **響應式設計測試**
  - 測試移動端、平板、桌面三種斷點
  - 驗證跨設備操作一致性
  - 測試篩選器響應式適配

- [ ] **整合功能測試**
  - 測試主頁面和 Partner 頁面操作一致性
  - 驗證篩選搜尋功能完整性
  - 測試 API 調用正常運作

**驗收條件**：
- ✅ 所有表單模式功能正常
- ✅ 資料流轉準確率 100%
- ✅ 響應式設計完美適配
- ✅ 前後端驗證規則 100% 同步
- ✅ 組件重用率 > 90%
- ✅ 代碼重複率 < 10%

## 技術實施細節

### ResponsiveModalConfig 使用
```typescript
import { getResponsiveModalConfig } from '@/app/ims/components/shared/ResponsiveModalConfig';

const ContactFormModal = ({ mode, ...props }) => {
  const { screenSize } = useScreenSize();
  const responsiveConfig = getResponsiveModalConfig(screenSize);
  
  return (
    <Modal
      width={responsiveConfig.modalWidth}
      style={responsiveConfig.modalStyle}
      styles={{ body: { padding: 0, maxHeight: responsiveConfig.modalMaxHeight, overflow: 'auto' } }}
    >
      {/* 表單內容 */}
    </Modal>
  );
};
```

### FilterSearchContainer 整合
```typescript
<FilterSearchContainer
  ref={filterSearchRef}
  title="聯絡人篩選與搜尋"
  filterOptions={contactFilterOptions}
  searchPlaceholder={isMobile ? "搜尋聯絡人" : "搜尋聯絡人姓名、電子郵件"}
  showStats={true}
  stats={{
    total: data.contacts.length,
    filtered: filteredContacts.length
  }}
  onFilterResult={(state) => {
    const filtered = applyContactFilters(
      data.contacts,
      state.searchText,
      state.activeFilters,
      state.filterValues
    );
    setFilteredContacts(filtered);
  }}
  compact={isMobile}
/>
```

## 風險評估與緩解

### 風險一：現有功能中斷
**風險等級**：中等
**緩解方案**：
- 分階段實施，確保每個階段都能獨立運行
- 保留原有組件作為備份，直到新組件完全穩定
- 充分測試每個變更

### 風險二：響應式設計問題
**風險等級**：低等
**緩解方案**：
- 使用已驗證的 ResponsiveModalConfig
- 參考 Item 模組的成功實踐
- 在多種設備上進行測試

### 風險三：使用者體驗變化
**風險等級**：低等
**緩解方案**：
- 保持核心操作流程不變
- 只優化和統一，不改變基本功能
- 提供平滑的過渡體驗

## 時程安排

| 階段 | 工作項目 | 預計時間 | 負責人 |
|------|----------|----------|--------|
| 階段一 | ContactFormModal 重構 | 1.5天 | 前端開發者 |
| 階段一 | 篩選器統一重構 | 1天 | 前端開發者 |
| 階段一 | ContactTable 優化 | 0.5天 | 前端開發者 |
| 階段二 | Partner 頁面優化 | 1天 | 前端開發者 |
| 階段二 | 主頁面優化 | 1天 | 前端開發者 |
| 階段三 | 組件移至 shared | 0.5天 | 前端開發者 |
| 階段三 | 文檔更新 | 0.5天 | 前端開發者 |
| **總計** | | **6天** | |

## 驗收檢查清單

### 功能驗收
- [ ] 所有 Contact 相關功能正常運作
- [ ] 新增、編輯、刪除聯絡人功能正常
- [ ] 篩選和搜尋功能正常
- [ ] Partner 關聯功能正常

### 技術驗收
- [ ] 無 TypeScript 編譯錯誤
- [ ] 無建置警告
- [ ] 代碼符合 FastERP 規範
- [ ] 組件重用率提升

### 使用者體驗驗收
- [ ] 響應式設計在所有斷點正常
- [ ] 操作流程順暢直觀
- [ ] 錯誤處理和提示統一
- [ ] 視覺設計一致

### 文檔驗收
- [ ] 使用指南更新完整
- [ ] 範例代碼正確
- [ ] API 文檔準確
