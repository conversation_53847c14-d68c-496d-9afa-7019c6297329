# IMS-Partner-004 新增/編輯商業夥伴時的聯絡人管理完善

## 任務摘要
- 目標：完善商業夥伴新增/編輯流程中的「聯絡人管理」，確保資料一致性、提交流程正確、最佳化使用者體驗。
- 狀態：In Progress
- 模組：IMS → Partner（前端/後端整合）

## 背景
先前已實作：
- IMS-Partner-001/002/003 完成聚合、後端模型與前端 UI
- IMS-Contact-001 完成聯絡人管理（前端客製統計/篩選）

目前發現：
- 在 Partner 聚合提交時，`partnerContacts` 需在前端正規化、驗證（單一主要、Priority 合理、去重）
- 快速新增聯絡人後的本地關聯需與最終提交一致化

## 範圍（Scope）
- 前端：`PartnerFormModal.tsx`、`ContactManagementTab.tsx`
- 服務：`PartnerContactService.ts`（僅必要時調整訊息/驗證字串）
- 文檔：本任務文件與驗收結果

## 驗收標準（DoD）
1. 新增/編輯夥伴時，`partnerContacts` 提交前會自動：
   - 去重（同一 `contactID` 僅一筆）
   - 僅一位 `isPrimary=true`（若多筆，選 Priority 最小為主要；若無，指定 Priority 最小者為主要）
   - `isPrimary` 的 `priority=0`，其餘未指定為 99
2. 夥伴資料載入時可正確回填聯絡人關聯（含主要與排序）。
3. 快速新增聯絡人後，會即時關聯至當前夥伴並納入最終提交。
4. 透過 UI 完成：新增/編輯/刪除/排序/設定主要，提交成功無型別或驗證錯誤。

## 風險/不在範圍
- 不新增後端統計/篩選 API（前端處理）
- 不改動後端 PartnerContact 模型結構

## 相關任務
- IMS-Partner-001/002/003、IMS-Contact-001


