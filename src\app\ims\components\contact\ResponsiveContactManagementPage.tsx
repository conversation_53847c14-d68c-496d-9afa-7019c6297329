"use client";

import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Space,
  Typography,
  <PERSON>ton,
  Tabs,
  Badge,
  Stati<PERSON>,
  Divider,
  Al<PERSON>,
  Spin,
  Modal
} from 'antd';
import {
  ContactsOutlined,
  UserAddOutlined,
  SettingOutlined,
  InfoCircleOutlined,
  ReloadOutlined,
  ExportOutlined,
  ImportOutlined
} from '@ant-design/icons';

// 響應式組件
import ResponsiveContactManagementCenter from '../shared/ResponsiveContactManagementCenter';
import ContactTable from './ContactTable';
import FilterSearchContainer from '../shared/FilterSearchContainer';

// 上下文和服務
import { useContactContext } from '@/contexts/ContactContext';
import { Contact } from '@/services/ims/ContactService';
import { useResponsive } from '@/hooks/useResponsive';
import { logger, SYMBOLS, createContextLogger } from '@/utils/logger';

// 創建上下文日誌器
const responsivePageLogger = createContextLogger({ module: 'ResponsiveContactManagementPage' });

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;

// 組件屬性介面
interface ResponsiveContactManagementPageProps {
  className?: string;
  showStatistics?: boolean;
  showExport?: boolean;
  showImport?: boolean;
}

const ResponsiveContactManagementPage: React.FC<ResponsiveContactManagementPageProps> = ({
  className = '',
  showStatistics = true,
  showExport = true,
  showImport = true
}) => {
  // 響應式 Hook
  const { isMobile, isTablet, isDesktop } = useResponsive();
  
  // 上下文
  const { 
    contacts, 
    loading, 
    error, 
    refreshContacts,
    searchContacts 
  } = useContactContext();

  // 本地狀態
  const [activeTab, setActiveTab] = useState('management');
  const [searchKeyword, setSearchKeyword] = useState('');
  const [filteredContacts, setFilteredContacts] = useState<Contact[]>([]);
  const [selectedContacts, setSelectedContacts] = useState<Contact[]>([]);
  const [showManagementCenter, setShowManagementCenter] = useState(false);

  // 初始化載入聯絡人
  useEffect(() => {
    refreshContacts();
  }, [refreshContacts]);

  // 搜尋和篩選聯絡人
  useEffect(() => {
    if (searchKeyword.trim()) {
      const filtered = searchContacts(searchKeyword);
      setFilteredContacts(filtered);
    } else {
      setFilteredContacts(contacts);
    }
  }, [contacts, searchKeyword, searchContacts]);

  // 計算統計資料
  const statistics = {
    total: contacts.length,
    active: contacts.filter(c => c.isActive).length,
    inactive: contacts.filter(c => !c.isActive).length,
    internal: contacts.filter(c => c.contactType === 'internal').length,
    external: contacts.filter(c => c.contactType === 'external').length,
    customer: contacts.filter(c => c.contactType === 'customer').length,
    supplier: contacts.filter(c => c.contactType === 'supplier').length,
    partner: contacts.filter(c => c.contactType === 'partner').length
  };

  // 處理聯絡人選擇
  const handleContactSelect = (contact: Contact) => {
    responsivePageLogger.log(SYMBOLS.SUCCESS, '聯絡人已選擇', { contactId: contact.contactID });
    setSelectedContacts(prev => {
      const exists = prev.find(c => c.contactID === contact.contactID);
      if (exists) {
        return prev.filter(c => c.contactID !== contact.contactID);
      } else {
        return [...prev, contact];
      }
    });
  };

  // 處理聯絡人編輯
  const handleContactEdit = (contact: Contact) => {
    responsivePageLogger.log(SYMBOLS.SUCCESS, '開始編輯聯絡人', { contactId: contact.contactID });
    // 這裡可以打開編輯表單或導航到編輯頁面
  };

  // 處理聯絡人刪除
  const handleContactDelete = async (contactId: string) => {
    try {
      responsivePageLogger.log(SYMBOLS.LOADING, '開始刪除聯絡人', { contactId });
      // 這裡可以調用刪除 API
      await refreshContacts();
    } catch (error) {
      responsivePageLogger.log(SYMBOLS.ERROR, '刪除聯絡人失敗', error);
    }
  };

  // 處理重新整理
  const handleRefresh = async () => {
    try {
      responsivePageLogger.log(SYMBOLS.LOADING, '開始重新整理聯絡人資料');
      await refreshContacts();
    } catch (error) {
      responsivePageLogger.log(SYMBOLS.ERROR, '重新整理失敗', error);
    }
  };

  // 處理匯出
  const handleExport = () => {
    responsivePageLogger.log(SYMBOLS.SUCCESS, '開始匯出聯絡人資料');
    // 這裡可以實現匯出功能
  };

  // 處理匯入
  const handleImport = () => {
    responsivePageLogger.log(SYMBOLS.SUCCESS, '開始匯入聯絡人資料');
    // 這裡可以實現匯入功能
  };

  // 渲染統計卡片
  const renderStatistics = () => {
    if (!showStatistics) return null;

    const statCards = [
      { title: '總聯絡人數', value: statistics.total, color: '#1890ff' },
      { title: '啟用聯絡人', value: statistics.active, color: '#52c41a' },
      { title: '停用聯絡人', value: statistics.inactive, color: '#ff4d4f' },
      { title: '內部員工', value: statistics.internal, color: '#722ed1' },
      { title: '外部聯絡人', value: statistics.external, color: '#fa8c16' },
      { title: '客戶', value: statistics.customer, color: '#13c2c2' },
      { title: '供應商', value: statistics.supplier, color: '#eb2f96' },
      { title: '合作夥伴', value: statistics.partner, color: '#fadb14' }
    ];

    return (
      <Card title="聯絡人統計" size="small" style={{ marginBottom: 16 }}>
        <Row gutter={[16, 16]}>
          {statCards.map((stat, index) => (
            <Col xs={12} sm={8} md={6} lg={3} key={index}>
              <Statistic
                title={stat.title}
                value={stat.value}
                valueStyle={{ color: stat.color, fontSize: isMobile ? '16px' : '20px' }}
              />
            </Col>
          ))}
        </Row>
      </Card>
    );
  };

  // 渲染操作按鈕
  const renderActionButtons = () => {
    return (
      <Space wrap>
        <Button
          icon={<ReloadOutlined />}
          onClick={handleRefresh}
          loading={loading}
          size={isMobile ? 'small' : 'middle'}
        >
          {isMobile ? '重新整理' : '重新整理資料'}
        </Button>
        {showExport && (
          <Button
            icon={<ExportOutlined />}
            onClick={handleExport}
            size={isMobile ? 'small' : 'middle'}
          >
            {isMobile ? '匯出' : '匯出資料'}
          </Button>
        )}
        {showImport && (
          <Button
            icon={<ImportOutlined />}
            onClick={handleImport}
            size={isMobile ? 'small' : 'middle'}
          >
            {isMobile ? '匯入' : '匯入資料'}
          </Button>
        )}
        <Button
          type="primary"
          icon={<UserAddOutlined />}
          onClick={() => setShowManagementCenter(true)}
          size={isMobile ? 'small' : 'middle'}
        >
          {isMobile ? '新增' : '新增聯絡人'}
        </Button>
      </Space>
    );
  };

  // 渲染聯絡人管理標籤頁
  const renderContactManagementTab = () => {
    return (
      <div>
        {/* 搜尋和篩選（統一共用容器） */}
        <FilterSearchContainer
          title="篩選與搜尋"
          searchPlaceholder="搜尋聯絡人（姓名、公司、職位、信箱）"
          filterOptions={[
            { label: '聯絡人類型', value: 'contactType', type: 'select', children: [
              { label: '內部員工', value: 'internal' },
              { label: '外部聯絡人', value: 'external' },
              { label: '客戶', value: 'customer' },
              { label: '供應商', value: 'supplier' },
              { label: '合作夥伴', value: 'partner' }
            ]},
            { label: '狀態', value: 'isActive', type: 'select', children: [
              { label: '啟用', value: 'true' },
              { label: '停用', value: 'false' }
            ]}
          ]}
          onFilterResult={(state) => {
            // 仍保留原有全文搜尋邏輯，並加入簡單的篩選示例
            setSearchKeyword(state.searchText || '');
            const list = state.searchText?.trim()
              ? searchContacts(state.searchText)
              : contacts;
            const filtered = list.filter(c => {
              // 類型
              if (state.filterValues.contactType && state.filterValues.contactType.length > 0) {
                if (!state.filterValues.contactType.includes(c.contactType)) return false;
              }
              // 狀態
              if (state.filterValues.isActive && state.filterValues.isActive.length > 0) {
                const expected = state.filterValues.isActive.map((v: string) => v === 'true');
                if (!expected.includes(!!c.isActive)) return false;
              }
              return true;
            });
            setFilteredContacts(filtered);
          }}
        />

        {/* 聯絡人表格 */}
        <ContactTable
          contacts={filteredContacts}
          loading={loading}
          onEdit={handleContactEdit}
          onDelete={handleContactDelete}
          showActions={true}
          pagination={{
            pageSize: isMobile ? 5 : 10,
            showSizeChanger: !isMobile,
            showQuickJumper: !isMobile,
            showTotal: (total: number, range: [number, number]) => 
              isMobile ? `${range[0]}-${range[1]}/${total}` : `第 ${range[0]}-${range[1]} 項，共 ${total} 項`
          }}
        />
      </div>
    );
  };

  // 渲染選中的聯絡人
  const renderSelectedContacts = () => {
    if (selectedContacts.length === 0) return null;

    return (
      <Card title="已選聯絡人" size="small" style={{ marginTop: 16 }}>
        <Space wrap>
          {selectedContacts.map(contact => (
            <Badge
              key={contact.contactID}
              count={1}
              color="blue"
            >
              <Card size="small" style={{ width: 200 }}>
                <Space direction="vertical" size={4}>
                  <Text strong>{contact.name}</Text>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    {contact.position} • {contact.company}
                  </Text>
                  <Text type="secondary" style={{ fontSize: '12px' }}>
                    {contact.email}
                  </Text>
                </Space>
              </Card>
            </Badge>
          ))}
        </Space>
      </Card>
    );
  };

  // 渲染錯誤訊息
  const renderError = () => {
    if (!error) return null;

    return (
      <Alert
        message="載入錯誤"
        description={error}
        type="error"
        showIcon
        style={{ marginBottom: 16 }}
        action={
          <Button size="small" onClick={handleRefresh}>
            重新載入
          </Button>
        }
      />
    );
  };

  return (
    <div className={`responsive-contact-management-page ${className}`}>
      {/* 頁面標題 */}
      <Card style={{ marginBottom: 16 }}>
        <Row justify="space-between" align="middle">
          <Col>
            <Space direction="vertical" size="small">
              <Space>
                <ContactsOutlined />
                <Title level={2} style={{ margin: 0 }}>聯絡人管理</Title>
                <Badge count={statistics.total} />
              </Space>
              <Text type="secondary">管理系統中的所有聯絡人資訊</Text>
            </Space>
          </Col>
          <Col>
            {renderActionButtons()}
          </Col>
        </Row>
      </Card>

      {/* 錯誤訊息 */}
      {renderError()}

      {/* 統計資料 */}
      {renderStatistics()}

      {/* 主要內容 */}
      <Card>
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          size={isMobile ? 'small' : 'middle'}
        >
          <TabPane
            tab={
              <Space>
                <ContactsOutlined />
                <span>聯絡人列表</span>
                <Badge count={filteredContacts.length} />
              </Space>
            }
            key="management"
          >
            {renderContactManagementTab()}
          </TabPane>
          
          <TabPane
            tab={
              <Space>
                <SettingOutlined />
                <span>進階管理</span>
              </Space>
            }
            key="advanced"
          >
            <div style={{ textAlign: 'center', padding: '40px 0' }}>
              <InfoCircleOutlined style={{ fontSize: 48, color: '#d9d9d9', marginBottom: 16 }} />
              <div style={{ color: '#999' }}>進階管理功能開發中...</div>
            </div>
          </TabPane>
        </Tabs>
      </Card>

      {/* 選中的聯絡人 */}
      {renderSelectedContacts()}

      {/* 聯絡人管理中心 Modal */}
      <Modal
        title="聯絡人管理中心"
        open={showManagementCenter}
        onCancel={() => setShowManagementCenter(false)}
        footer={null}
        width="95%"
        style={{ maxWidth: '1400px' }}
        destroyOnClose
        centered
      >
        <ResponsiveContactManagementCenter
          mode="standalone"
          onClose={() => setShowManagementCenter(false)}
          onContactCreate={(contact) => {
            setShowManagementCenter(false);
            handleRefresh();
          }}
        />
      </Modal>
    </div>
  );
};

export default ResponsiveContactManagementPage;
