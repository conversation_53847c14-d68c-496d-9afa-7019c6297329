# IMS-Contact-005：Contact 與 PartnerContact 操作流程優化

## 任務基本資訊
- 任務代碼：IMS-Contact-005
- 任務標題：Contact 與 PartnerContact 操作流程優化
- 所屬模組：IMS（進銷存管理系統）
- 需求提出：系統架構師
- 相關任務：基於 IMS-Contact-004 完成狀況和操作流程優化討論

## 任務概述
基於 IMS-Contact-004 的完成狀況和深入的操作流程分析，優化 Contact 與 PartnerContact 的整合操作流程，解決現有的操作複雜性和資料同步問題，提供更流暢的用戶體驗。

**🔄 任務背景**：
IMS-Contact-004 已完成 80% 的組件標準化工作，但缺乏 Contact 與 PartnerContact 的整合優化。本任務專注於解決操作流程斷裂、資料同步問題和交易完整性需求。

## 核心目標

### 1. 統一操作入口
- **建立 ContactManagementCenter**：統一 Contact 和 PartnerContact 操作
- **支援多種使用場景**：獨立管理、夥伴整合、快速關聯
- **智能操作指引**：根據使用情境提供操作建議

### 2. 簡化操作流程
- **一鍵新增聯絡人並關聯**：減少操作步驟，提升效率
- **統一表單模式**：create/quick/edit 模式清晰區分
- **優化關聯管理**：簡化 PartnerContact 的建立和編輯

### 3. 確保交易完整性
- **後端交易支援**：使用 TransactionScope 確保資料一致性
- **前端錯誤處理**：提供回滾機制和用戶指引
- **資料同步優化**：使用 Context 管理聯絡人狀態

### 4. 提升用戶體驗
- **操作流程統一**：消除不同入口的功能重複
- **響應式優化**：改進移動端操作體驗
- **智能提示**：提供操作建議和錯誤指引

## 現狀問題分析

### 操作流程問題
- **流程斷裂**：Contact 管理 → 獨立頁面 → 無法直接關聯到 Partner
- **操作複雜**：需要先選擇聯絡人，再設定關聯屬性
- **重複操作**：快速新增後還要重新載入聯絡人清單

### 資料同步問題
- **狀態分散**：多個組件重複載入相同的聯絡人資料
- **同步困難**：Contact 新增後，PartnerContact 清單不會自動更新
- **一致性問題**：多個組件間狀態難以保持同步

### 交易完整性問題
- **缺乏交易保護**：前端分步操作沒有交易完整性保證
- **資料不一致風險**：聯絡人新增成功但關聯失敗
- **回滾困難**：需要手動清理失敗的資料

## 重構計劃

### 階段一：後端 API 優化與交易完整性支援（1-2天）

#### 1.1 修正現有 API 路由問題
- **修正 PartnerContactController 路由**：使用 `[Route("api/[controller]")]` 讓 ModuleRouteConvention 自動處理
- **優化 HTTP 方法設計**：遵循 RESTful 原則，使用明確的動詞
- **統一 API 命名規範**：使用 `associate`、`unassociate`、`add-and-associate` 等明確動詞
- **同步前端 API 路由**：確保前後端路由一致性

#### 1.2 新增 PartnerService 方法
- **AddContactAndAssociateAsync**：一次性新增聯絡人並建立關聯
- **使用 TransactionScope**：確保交易完整性
- **統一錯誤處理**：提供詳細的錯誤訊息

#### 1.3 優化 API 設計
- **遵循開發準則**：使用 `[Route("api/[controller]")]` 格式
- **統一參數設計**：所有參數在 Body 中，避免 URL 複雜度
- **重用現有架構**：基於 GenericController 和 GenericService

### 階段二：前端整合優化（2-3天）

#### 2.1 建立 ContactContext
- **全域狀態管理**：統一聯絡人資料管理
- **減少重複載入**：避免多個組件重複 API 呼叫
- **即時同步**：操作後自動更新相關組件

#### 2.2 建立 ContactManagementCenter
- **統一操作入口**：支援多種使用場景
- **智能模式選擇**：根據情境自動選擇最佳操作模式
- **操作指引**：提供清晰的操作流程提示

#### 2.3 優化現有組件
- **ContactManagementTab**：整合到 ContactContext
- **PartnerFormModal**：整合 ContactManagementCenter
- **ContactFormModal**：優化模式切換和資料流轉

### 階段三：用戶體驗優化（1-2天）

#### 3.1 操作流程統一
- **消除功能重複**：統一主頁面和 Partner 頁面的操作
- **簡化關聯流程**：一鍵完成聯絡人新增和關聯
- **智能預設值**：根據情境自動填入相關欄位

#### 3.2 響應式優化
- **移動端適配**：優化小螢幕操作體驗
- **觸控操作**：支援拖拽排序和觸控操作
- **載入狀態**：提供清晰的載入和錯誤提示

## 技術規範

### 後端設計標準
- **交易完整性**：使用 TransactionScope 確保資料一致性
- **錯誤處理**：統一的錯誤回應格式和日誌記錄
- **API 設計**：遵循 RESTful 原則，簡潔明瞭的路由命名

### 前端設計標準
- **狀態管理**：使用 Context 統一管理聯絡人狀態
- **組件重用**：最大化重用現有組件和工具函數
- **響應式設計**：支援移動端、平板、桌面三種斷點

### 資料流設計
- **單向資料流**：父組件向子組件傳遞資料
- **事件回調**：子組件通過回調函數通知父組件
- **狀態同步**：多個組件間狀態保持同步

## 驗收標準

### 功能驗收
- ✅ 一鍵新增聯絡人並關聯功能正常運作
- ✅ 交易完整性保證，無資料不一致問題
- ✅ 所有操作流程統一，無重複功能
- ✅ 資料同步正常，多組件狀態一致

### 用戶體驗驗收
- ✅ 操作流程順暢，步驟簡化
- ✅ 錯誤處理完善，提供清晰指引
- ✅ 響應式設計在所有斷點正常
- ✅ 載入狀態和成功提示統一

### 技術品質驗收
- ✅ 代碼重用率提升，減少重複代碼
- ✅ TypeScript 類型安全，無編譯錯誤
- ✅ 符合 FastERP 編碼規範
- ✅ 文檔完整準確

## 預期成果
- **操作效率提升**：減少 50% 的操作步驟
- **資料一致性**：100% 交易完整性保證
- **用戶體驗統一**：所有 Contact 相關功能具有一致的操作體驗
- **維護成本降低**：統一狀態管理，減少重複代碼

## 目錄
- [specs/](./specs/)：需求與業務規格
- [plan.md](./plan.md)：詳細實作規劃
- [status.md](./status.md)：進度追蹤
- [analysis/](./analysis/)：架構分析和評估
- [improvement/](./improvement/)：改進計畫和追蹤
- [testing/](./testing/)：API 測試和驗證
