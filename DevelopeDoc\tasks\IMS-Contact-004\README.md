# IMS-Contact-004：Contact 組件標準化重構

## 任務基本資訊
- 任務代碼：IMS-Contact-004
- 任務標題：Contact 組件標準化重構
- 所屬模組：IMS（進銷存管理系統）
- 需求提出：系統架構師
- 相關任務：基於 IMS-Contact-003 現狀分析結果

## 任務概述
基於現狀分析結果，對 Contact 相關組件進行標準化重構，確保完全符合 IMS 標準和最佳實踐，參考 Item 模組的設計模式，實現組件重用最大化和使用者體驗一致性。

**🔄 任務整合說明**：
本任務已整合 IMS-Contact-003 的剩餘工作項目（表單欄位統一、資料流轉優化、測試驗證），形成增強版重構任務。通過合併執行，避免 95% 的重複工作，節省 25% 開發時間，同時達成兩個任務的所有目標。

## 核心目標

### 1. 組件標準化
- **最大化重用 IMS 共通組件**：FilterSearchContainer、ResponsiveTable、ResponsiveModalConfig
- **遵循 Item 模組設計模式**：統一的篩選、表格、表單設計
- **統一響應式設計**：支援移動端 (≤768px)、平板 (769-1024px)、桌面 (>1024px)

### 2. 表單統一設計（整合 IMS-Contact-003 階段三）
- **解決表單欄位不一致問題**：統一完整新增和快速新增的欄位設計
- **統一驗證規則**：確保前後端驗證規則完全同步（已完成）
- **情境化表單模式**：支援 create、quick、edit 三種模式
- **快速新增欄位補強**：增加職位、電話、部門、公司欄位到快速模式

### 3. 使用者體驗優化
- **統一操作流程**：消除主頁面和 Partner 頁面的功能重複
- **簡化聯絡人管理**：優化 Partner 頁面的聯絡人關聯流程
- **一致性設計**：確保所有 Contact 相關功能的視覺和操作一致性

### 4. 資料流轉優化（整合 IMS-Contact-003 階段四）
- **完整資料流轉**：確保快速新增的資料能完整帶入編輯表單
- **預設值處理統一**：統一所有表單的預設值處理邏輯
- **欄位映射優化**：處理不同表單模式間的欄位映射問題

## 現狀問題分析

### 組件重用問題
- ✅ ContactTable.tsx 已使用 ResponsiveTable
- ❌ ContactFormModal.tsx 未使用 ResponsiveModalConfig
- ❌ ContactFilters.tsx 未使用 FilterSearchContainer
- ❌ ContactSearchForm.tsx 未使用 AdvancedFilterComponent

### 表單欄位不一致問題
**完整新增聯絡人表單（主頁面）**：8個欄位
- 姓名（必填）、職位、電子郵件、電話、部門、公司、聯絡人類型、狀態

**快速新增聯絡人表單（Partner 頁面）**：8個欄位（分為兩部分）
- 基本欄位：姓名（必填）、電子郵件、聯絡人類型、狀態
- PartnerContact 欄位：角色（必填）、優先序（必填）、專業領域、備註、業務範圍
- **缺少**：職位、電話、部門、公司欄位

### 使用者體驗問題
- **功能重複**：主頁面和 Partner 編輯頁面都有新增聯絡人功能
- **操作流程不一致**：不同入口的表單欄位和操作流程不同
- **篩選功能分散**：多個獨立的篩選組件，未統一管理

## 重構計劃

### 階段一：組件標準化重構（2-3天）

#### 1.1 ContactFormModal 重構
- **使用 ResponsiveModalConfig**：統一響應式設計
- **參考 ItemFormModal 設計模式**：統一表單結構和驗證
- **支援多種模式**：
  - `create`：完整新增模式（8個基本欄位）
  - `quick`：快速新增模式（4個基本欄位 + 5個 PartnerContact 欄位）
  - `edit`：編輯模式（根據情境顯示相應欄位）

#### 1.2 篩選器統一重構
- **使用 FilterSearchContainer**：取代 ContactFilters 和 ContactSearchForm
- **定義標準篩選選項**：使用 CommonFilterOptions 模板
- **支援搜尋功能**：聯絡人姓名、電子郵件、公司、部門
- **支援篩選功能**：聯絡人類型、狀態、部門、公司

### 階段二：操作流程優化（1-2天）

#### 2.1 Partner 頁面聯絡人管理優化
- **統一新增聯絡人入口**：使用相同的 ContactFormModal 組件
- **優化快速新增流程**：保持快速性但增加完整性
- **改善關聯管理**：簡化 PartnerContact 的建立和編輯流程

#### 2.2 表單欄位統一設計
- **基礎欄位**（所有模式）：姓名*、電子郵件、聯絡人類型、狀態
- **詳細欄位**（完整模式）：職位、電話、部門、公司
- **關聯欄位**（PartnerContact 模式）：角色*、優先序*、專業領域、備註、業務範圍

### 階段三：共享組件提取（1天）

#### 3.1 提取可重用組件
- **移至 shared 目錄**：將優化後的組件移至 src/app/ims/components/shared/
- **建立組件庫**：統一的 Contact 管理組件庫
- **文檔更新**：更新使用指南和範例

## 技術規範

### 響應式設計標準
- **移動端** (≤768px)：簡化佈局，垂直排列
- **平板** (769-1024px)：適中佈局，部分水平排列
- **桌面** (>1024px)：完整佈局，水平排列

### 組件使用標準
- **FilterSearchContainer**：統一篩選和搜尋功能
- **ResponsiveTable**：統一表格顯示
- **ResponsiveModalConfig**：統一模態框響應式配置
- **CommonFilterOptions**：統一篩選選項定義

### 驗證規則標準
- **前後端同步**：確保驗證規則完全一致
- **統一錯誤訊息**：使用相同的錯誤訊息格式
- **類型安全**：完整的 TypeScript 類型定義

## 驗收標準

### 組件標準化驗收
- ✅ ContactFormModal 使用 ResponsiveModalConfig
- ✅ 篩選功能使用 FilterSearchContainer
- ✅ 所有組件遵循 Item 模組的設計模式
- ✅ 響應式設計在所有斷點正常工作

### 表單統一性驗收
- ✅ 所有新增聯絡人表單使用相同的欄位結構
- ✅ 快速新增和完整新增功能一致
- ✅ Partner 頁面和主頁面的操作流程統一

### 使用者體驗驗收
- ✅ 操作流程順暢，無重複功能
- ✅ 表單驗證規則一致
- ✅ 錯誤處理和成功提示統一

### 技術品質驗收
- ✅ 代碼重用率提升，減少重複代碼
- ✅ TypeScript 類型安全
- ✅ 無建置錯誤和警告
- ✅ 符合 FastERP 編碼規範

## 預期成果
- **組件重用率提升**：最大化使用 IMS 共通組件
- **使用者體驗統一**：所有 Contact 相關功能具有一致的操作體驗
- **維護成本降低**：減少重複代碼，提高代碼品質
- **擴展性增強**：為其他模組提供可重用的 Contact 管理組件

## 目錄
- [specs/](./specs/)：需求與業務規格
- [plan.md](./plan.md)：詳細實作規劃
- [status.md](./status.md)：進度追蹤
- [improvement/](./improvement/)：改進計畫和追蹤
