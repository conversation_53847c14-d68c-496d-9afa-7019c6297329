"use client";

import React, { createContext, useContext, useState, useCallback, ReactNode } from 'react';
import { Contact } from '@/services/ims/ContactService';
import { PartnerContact } from '@/services/ims/partner';
import { getContactList, addContact, updateContact, deleteContact } from '@/services/ims/ContactService';
import { message } from 'antd';
import { logger, SYMBOLS, createContextLogger } from '@/utils/logger';

// 創建上下文日誌器
const contactContextLogger = createContextLogger({ module: 'ContactContext' });

// 聯絡人上下文類型定義
interface ContactContextType {
  // 狀態
  contacts: Contact[];
  loading: boolean;
  error: string | null;
  
  // 操作
  refreshContacts: () => Promise<void>;
  addContact: (contact: Omit<Contact, 'contactID' | 'createTime' | 'updateTime' | 'isDeleted'>) => Promise<Contact | null>;
  updateContact: (contact: Contact) => Promise<boolean>;
  deleteContact: (contactId: string) => Promise<boolean>;
  
  // 查詢
  getContactById: (contactId: string) => Contact | undefined;
  searchContacts: (keyword: string) => Contact[];
  
  // 狀態管理
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
}

// 建立上下文
const ContactContext = createContext<ContactContextType | undefined>(undefined);

// 聯絡人提供者組件
interface ContactProviderProps {
  children: ReactNode;
}

export const ContactProvider: React.FC<ContactProviderProps> = ({ children }) => {
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 重新載入聯絡人清單
  const refreshContacts = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      contactContextLogger.log(SYMBOLS.LOADING, '開始載入聯絡人清單');
      
      const response = await getContactList();
      
      if (response.success && response.data) {
        setContacts(response.data);
        contactContextLogger.log(SYMBOLS.SUCCESS, '聯絡人清單載入成功', { count: response.data.length });
      } else {
        throw new Error(response.message || '載入聯絡人清單失敗');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '載入聯絡人清單失敗';
      setError(errorMessage);
      contactContextLogger.log(SYMBOLS.ERROR, '聯絡人清單載入失敗', err);
      message.error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  // 新增聯絡人
  const addContactHandler = useCallback(async (contactData: Omit<Contact, 'contactID' | 'createTime' | 'updateTime' | 'isDeleted'>): Promise<Contact | null> => {
    setLoading(true);
    setError(null);
    
    try {
      contactContextLogger.log(SYMBOLS.LOADING, '開始新增聯絡人', { name: contactData.name });
      
      const response = await addContact(contactData);
      
      if (response.success && response.data) {
        const newContact = response.data;
        setContacts(prev => [...prev, newContact]);
        contactContextLogger.log(SYMBOLS.SUCCESS, '聯絡人新增成功', { contactId: newContact.contactID });
        message.success('聯絡人新增成功');
        return newContact;
      } else {
        throw new Error(response.message || '新增聯絡人失敗');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '新增聯絡人失敗';
      setError(errorMessage);
      contactContextLogger.log(SYMBOLS.ERROR, '聯絡人新增失敗', err);
      message.error(errorMessage);
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  // 更新聯絡人
  const updateContactHandler = useCallback(async (contact: Contact): Promise<boolean> => {
    setLoading(true);
    setError(null);
    
    try {
      contactContextLogger.log(SYMBOLS.LOADING, '開始更新聯絡人', { contactId: contact.contactID, name: contact.name });
      
      const response = await updateContact(contact);
      
      if (response.success) {
        setContacts(prev => prev.map(c => c.contactID === contact.contactID ? contact : c));
        contactContextLogger.log(SYMBOLS.SUCCESS, '聯絡人更新成功', { contactId: contact.contactID });
        message.success('聯絡人更新成功');
        return true;
      } else {
        throw new Error(response.message || '更新聯絡人失敗');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '更新聯絡人失敗';
      setError(errorMessage);
      contactContextLogger.log(SYMBOLS.ERROR, '聯絡人更新失敗', err);
      message.error(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  // 刪除聯絡人
  const deleteContactHandler = useCallback(async (contactId: string): Promise<boolean> => {
    setLoading(true);
    setError(null);
    
    try {
      contactContextLogger.log(SYMBOLS.LOADING, '開始刪除聯絡人', { contactId });
      
      const response = await deleteContact(contactId);
      
      if (response.success) {
        setContacts(prev => prev.filter(c => c.contactID !== contactId));
        contactContextLogger.log(SYMBOLS.SUCCESS, '聯絡人刪除成功', { contactId });
        message.success('聯絡人刪除成功');
        return true;
      } else {
        throw new Error(response.message || '刪除聯絡人失敗');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '刪除聯絡人失敗';
      setError(errorMessage);
      contactContextLogger.log(SYMBOLS.ERROR, '聯絡人刪除失敗', err);
      message.error(errorMessage);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  // 根據ID取得聯絡人
  const getContactById = useCallback((contactId: string): Contact | undefined => {
    return contacts.find(c => c.contactID === contactId);
  }, [contacts]);

  // 搜尋聯絡人
  const searchContacts = useCallback((keyword: string): Contact[] => {
    if (!keyword.trim()) return contacts;
    
    const lowerKeyword = keyword.toLowerCase();
    return contacts.filter(contact =>
      contact.name.toLowerCase().includes(lowerKeyword) ||
      (contact.email && contact.email.toLowerCase().includes(lowerKeyword)) ||
      (contact.company && contact.company.toLowerCase().includes(lowerKeyword)) ||
      (contact.position && contact.position.toLowerCase().includes(lowerKeyword))
    );
  }, [contacts]);

  // 上下文值
  const contextValue: ContactContextType = {
    contacts,
    loading,
    error,
    refreshContacts,
    addContact: addContactHandler,
    updateContact: updateContactHandler,
    deleteContact: deleteContactHandler,
    getContactById,
    searchContacts,
    setLoading,
    setError
  };

  return (
    <ContactContext.Provider value={contextValue}>
      {children}
    </ContactContext.Provider>
  );
};

// 使用聯絡人上下文的 Hook
export const useContactContext = (): ContactContextType => {
  const context = useContext(ContactContext);
  
  if (!context) {
    throw new Error('useContactContext must be used within ContactProvider');
  }
  
  return context;
};

// 聯絡人狀態管理 Hook（用於非 Provider 環境）
export const useContactState = () => {
  const [contacts, setContacts] = useState<Contact[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const refreshContacts = useCallback(async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await getContactList();
      
      if (response.success && response.data) {
        setContacts(response.data);
      } else {
        throw new Error(response.message || '載入聯絡人清單失敗');
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : '載入聯絡人清單失敗';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  return {
    contacts,
    loading,
    error,
    refreshContacts,
    setContacts,
    setLoading,
    setError
  };
};

export default ContactContext;
