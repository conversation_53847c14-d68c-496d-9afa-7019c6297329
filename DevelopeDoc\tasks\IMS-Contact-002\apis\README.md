# IMS-Contact-002 API 需求

## API 變更概述

本次重構主要是前端架構優化，**不涉及後端 API 的變更**。所有現有的 API 端點保持不變，確保向後相容性。

## 現有 API 端點確認

### 聯絡人管理 API（ContactController）
基於 Generic Controller，提供標準 CRUD 操作：

```http
# 基本 CRUD 操作
GET    /api/ims/Contact              # 取得聯絡人清單
GET    /api/ims/Contact/{id}         # 取得單一聯絡人
POST   /api/ims/Contact              # 新增聯絡人
PUT    /api/ims/Contact/{id}         # 更新聯絡人
DELETE /api/ims/Contact/{id}         # 刪除聯絡人

# 搜尋功能
GET    /api/ims/Contact/search?keyword={keyword}  # 搜尋聯絡人
```

### 夥伴聯絡人關聯 API（PartnerContactController）
管理夥伴與聯絡人的關聯關係：

```http
# 關聯管理
GET    /api/ims/PartnerContact                    # 取得關聯清單
POST   /api/ims/PartnerContact                    # 建立關聯
PUT    /api/ims/PartnerContact/{id}               # 更新關聯
DELETE /api/ims/PartnerContact/{id}               # 刪除關聯

# 特定查詢
GET    /api/ims/PartnerContact/by-partner/{partnerId}  # 取得夥伴的所有聯絡人
```

## API 回應格式確認

### 聯絡人資料格式
```json
{
  "success": true,
  "message": "操作成功",
  "data": {
    "contactID": "string",
    "name": "string",
    "position": "string",
    "email": "string", 
    "phone": "string",
    "department": "string",
    "company": "string",
    "contactType": "string",
    "status": boolean,
    "createTime": number,
    "createUserId": "string",
    "updateTime": number,
    "updateUserId": "string",
    "deleteTime": number,
    "deleteUserId": "string",
    "isDeleted": boolean
  }
}
```

### 夥伴聯絡人關聯格式
```json
{
  "success": true,
  "message": "操作成功", 
  "data": {
    "partnerID": "string",
    "contactID": "string",
    "role": "string",
    "isPrimary": boolean,
    "priority": number,
    "notes": "string",
    "expertise": "string", 
    "businessScope": "string",
    "createTime": number,
    "createUserId": "string",
    "updateTime": number,
    "updateUserId": "string",
    "deleteTime": number,
    "deleteUserId": "string",
    "isDeleted": boolean
  }
}
```

## 已知的 API 問題與解決方案

### 問題 1：addContact 回傳 data: null
**問題描述**：在某些環境下，`POST /api/ims/Contact` 成功執行但回傳 `data: null`

**影響範圍**：快速新增聯絡人功能

**前端解決方案**：
- 在 `ContactService.ts` 中實作 `normalizeContact` 函數處理資料格式不一致
- 在 `contactUtils.ts` 中實作 `resolveCreatedContact` 後援查找機制
- 當 `data: null` 時，使用搜尋 API 找回剛建立的聯絡人

**程式碼範例**：
```typescript
// ContactService.ts
const normalizeContact = (contact: any): Contact => {
  if (!contact) return createEmptyContact() as Contact;
  return {
    contactID: contact.contactID || contact.ContactID || '',
    // ... 其他欄位正規化
  };
};

// contactUtils.ts  
export const resolveCreatedContact = async (searchCriteria: {
  email?: string;
  name?: string;
}): Promise<Contact | null> => {
  // 使用搜尋 API 找回新建的聯絡人
};
```

### 問題 2：屬性名稱大小寫不一致
**問題描述**：後端可能回傳 `ContactID` 或 `contactID`

**前端解決方案**：
- 統一使用 `normalizeContact` 函數處理所有 API 回應
- 支援多種命名格式的自動轉換

## API 測試需求

### 現有測試檔案
- `Contact_API_Tests.http`：聯絡人 CRUD 測試
- `PartnerContact_API_Tests.http`：關聯管理測試

### 需要補充的測試案例
```http
### 測試聯絡人搜尋功能
GET {{baseUrl}}/api/ims/Contact/search?keyword=張
Authorization: Bearer {{token}}

### 測試建立聯絡人後的資料格式
POST {{baseUrl}}/api/ims/Contact
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "name": "測試聯絡人",
  "email": "<EMAIL>",
  "contactType": "客戶",
  "status": true
}

### 測試夥伴聯絡人查詢
GET {{baseUrl}}/api/ims/PartnerContact/by-partner/{{partnerId}}
Authorization: Bearer {{token}}
```

## 前端 API 呼叫優化

### 統一錯誤處理
```typescript
// ContactService.ts 中統一處理 API 錯誤
const handleApiError = (error: any, operation: string) => {
  contactServiceLogger.log(SYMBOLS.ERROR, `${operation}失敗`, error);
  return {
    success: false,
    message: error.message || `${operation}失敗，請重試`,
    data: null
  };
};
```

### 統一資料正規化
```typescript
// 所有 API 回應都通過 normalizeContact 處理
export const addContact = async (contactData: Partial<Contact>) => {
  try {
    const response = await apiCall('/api/ims/Contact', 'POST', contactData);
    if (response.success) {
      const normalizedData = normalizeContact(response.data);
      return { ...response, data: normalizedData };
    }
    return response;
  } catch (error) {
    return handleApiError(error, '新增聯絡人');
  }
};
```

## API 相容性保證

### 向後相容性承諾
- 所有現有的 API 端點繼續支援
- 現有的請求/回應格式不變
- 現有的錯誤碼和訊息不變

### 前端適配策略
- 使用適配器模式處理資料格式差異
- 漸進式重構，確保每個階段都能正常運作
- 完整的回歸測試確保功能不受影響

## 未來 API 改進建議

### 短期改進（可選）
- 統一後端回應的屬性命名格式（camelCase）
- 確保 `addContact` 始終回傳完整的聯絡人資料

### 長期改進（未來版本）
- 考慮實作批量操作 API（批量建立/更新聯絡人）
- 考慮實作更豐富的搜尋篩選 API
- 考慮實作聯絡人匯入/匯出功能

**注意**：這些改進不在本次重構範圍內，本次重構專注於前端架構優化。
