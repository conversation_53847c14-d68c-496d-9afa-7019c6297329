# IMS-Contact-002 需求規格

## 業務需求

### 使用者故事

#### 故事 1：統一的聯絡人表單體驗
**作為** 業務使用者  
**我希望** 在任何地方新增/編輯聯絡人時都有一致的體驗  
**這樣** 我就不需要重新學習不同的操作方式  

**驗收條件**：
- [ ] 快速新增聯絡人與完整新增聯絡人的欄位、選項、驗證規則完全一致
- [ ] 所有聯絡人類型選項在系統中保持一致
- [ ] 預設值行為在所有表單中統一

#### 故事 2：一致的搜尋和篩選體驗
**作為** 業務使用者  
**我希望** 在不同頁面搜尋聯絡人時有相同的篩選選項和搜尋邏輯  
**這樣** 我就能快速找到需要的聯絡人  

**驗收條件**：
- [ ] 所有聯絡人列表都使用相同的篩選器組件
- [ ] 搜尋邏輯在所有地方保持一致（多欄位搜尋）
- [ ] 篩選選項在所有地方保持一致

#### 故事 3：響應式和可訪問的聯絡人管理
**作為** 使用行動裝置的使用者  
**我希望** 聯絡人管理功能在手機上也能正常使用  
**這樣** 我就能隨時隨地管理聯絡人資料  

**驗收條件**：
- [ ] 所有聯絡人功能在移動端正常顯示和操作
- [ ] 表格在移動端自動切換為卡片模式
- [ ] 模態框在移動端適應螢幕尺寸

### 功能需求

#### FR-1：統一常數管理
- 所有聯絡人類型、角色、狀態選項集中定義
- 支援未來新增或修改選項時只需更新一處
- 與後端 API 回傳的選項保持同步

#### FR-2：統一驗證規則
- 前端驗證規則與後端 `ValidationHelper.cs` 保持一致
- 支援多語言錯誤訊息（未來擴展）
- 提供統一的驗證函數給所有表單使用

#### FR-3：共享組件重用
- 充分利用現有的 `AdvancedFilterComponent` 進行篩選
- 使用 `ResponsiveTable` 提供一致的表格體驗
- 使用 `FilterSearchContainer` 統一搜尋介面
- 使用 `ResponsiveModalConfig` 確保模態框響應式行為

#### FR-4：工具函數統一
- 資料正規化邏輯統一（處理後端資料格式不一致）
- 搜尋和篩選邏輯統一
- 聯絡人建立失敗時的後援查找邏輯統一

### 非功能需求

#### NFR-1：效能要求
- 組件重構後載入時間不得增加超過 10%
- 篩選和搜尋響應時間不得超過 300ms
- 支援大量聯絡人資料（1000+ 筆）的流暢操作

#### NFR-2：維護性要求
- 新增聯絡人類型只需修改常數定義檔案
- 修改驗證規則只需更新驗證函數
- 所有共享邏輯具有完整的 TypeScript 類型定義

#### NFR-3：相容性要求
- 現有的 API 介面不得變更
- 現有的使用者操作流程不得改變
- 支援現有的權限檢查機制

## 業務規則

### BR-1：聯絡人資料完整性
- 姓名為必填欄位，最大長度 100 字元
- 電子郵件必須符合標準格式，最大長度 255 字元
- 電話號碼最大長度 50 字元
- 其他文字欄位最大長度 100 字元

### BR-2：聯絡人狀態管理
- 新建聯絡人預設為啟用狀態
- 停用的聯絡人在選擇清單中應明確標示
- 刪除聯絡人採用軟刪除機制

### BR-3：夥伴聯絡人關聯
- 每個夥伴可以有多個聯絡人
- 每個夥伴最多只能有一個主要聯絡人
- 主要聯絡人的優先順序自動設為 0
- 非主要聯絡人的預設優先順序為 99

## 資料結構

### 聯絡人基本結構
```typescript
interface Contact {
  contactID: string;
  name: string;
  position?: string;
  email?: string;
  phone?: string;
  department?: string;
  company?: string;
  contactType: string;
  isActive: boolean;
  // 系統欄位
  createTime: number | null;
  createUserId: string | null;
  updateTime: number | null;
  updateUserId: string | null;
  deleteTime: number | null;
  deleteUserId: string | null;
  isDeleted: boolean;
}
```

### 夥伴聯絡人關聯結構
```typescript
interface PartnerContact {
  partnerID: string;
  contactID: string;
  role: string;
  isPrimary: boolean;
  priority: number;
  notes?: string;
  expertise?: string;
  businessScope?: string;
  // 系統欄位（繼承 ModelBaseEntityDTO）
  createTime: number | null;
  createUserId: string | null;
  updateTime: number | null;
  updateUserId: string | null;
  deleteTime: number | null;
  deleteUserId: string | null;
  isDeleted: boolean;
}
```

## 使用情境

### 情境 1：快速新增聯絡人
1. 使用者在編輯夥伴時需要新增聯絡人
2. 點擊「快速新增聯絡人」按鈕
3. 填寫聯絡人基本資訊（姓名、郵件、類型、狀態）
4. 系統驗證資料並建立聯絡人
5. 自動建立夥伴-聯絡人關聯
6. 如果建立失敗，系統嘗試後援查找

### 情境 2：管理聯絡人清單
1. 使用者進入聯絡人管理頁面
2. 使用篩選器縮小搜尋範圍（類型、狀態、公司等）
3. 使用搜尋框進行關鍵字搜尋
4. 在結果中選擇、編輯或刪除聯絡人
5. 在移動裝置上自動切換為卡片檢視模式

### 情境 3：編輯現有聯絡人
1. 使用者在聯絡人清單中選擇編輯
2. 系統載入聯絡人完整資訊
3. 使用者修改資訊並儲存
4. 系統驗證資料並更新
5. 相關的夥伴關聯也會同步更新

## 約束條件

### 技術約束
- 必須使用現有的共享組件系統
- 必須保持與後端 API 的相容性
- 必須支援 TypeScript 嚴格模式
- 必須遵循現有的程式碼規範

### 業務約束
- 不得影響現有使用者的操作習慣
- 不得降低系統效能
- 必須支援未來的多語言需求
- 必須支援現有的權限控制機制

## 驗收標準

### 功能驗收
- [ ] 所有聯絡人相關功能正常運作
- [ ] 新舊功能行為完全一致
- [ ] 響應式設計在各種螢幕尺寸下正常顯示
- [ ] 所有驗證規則正確執行

### 程式碼品質驗收
- [ ] 程式碼重複度降低 80% 以上
- [ ] 所有共享函數具有完整的單元測試
- [ ] TypeScript 編譯無錯誤和警告
- [ ] 通過 ESLint 和 Prettier 檢查

### 效能驗收
- [ ] 頁面載入時間不超過 2 秒
- [ ] 搜尋響應時間不超過 300ms
- [ ] 支援 1000+ 筆聯絡人資料流暢操作
- [ ] 記憶體使用量無明顯增加
