# IMS-Contact-004 需求規格

## 業務需求

### 核心問題
基於 IMS-Contact-003 現狀分析，Contact 相關組件存在以下問題：
1. **組件重用不足**：未充分使用 IMS 共通組件
2. **表單設計不一致**：不同入口的表單欄位和流程不同
3. **使用者體驗分散**：功能重複，操作流程不統一

### 業務目標
1. **提升開發效率**：最大化重用 IMS 共通組件，減少重複開發
2. **統一使用者體驗**：確保所有 Contact 相關功能具有一致的操作體驗
3. **降低維護成本**：減少重複代碼，提高代碼品質和可維護性
4. **增強系統擴展性**：為其他模組提供可重用的 Contact 管理組件

## 功能需求

### FR-001：統一表單設計
**描述**：統一所有 Contact 相關表單的設計和欄位配置

**需求詳情**：
- 支援三種表單模式：
  - `create`：完整新增模式（8個基本欄位）
  - `quick`：快速新增模式（4個基本欄位 + 5個 PartnerContact 欄位）
  - `edit`：編輯模式（根據情境顯示相應欄位）

**欄位定義**：
- **基礎欄位**（所有模式）：姓名*、電子郵件、聯絡人類型、狀態
- **詳細欄位**（完整模式）：職位、電話、部門、公司
- **關聯欄位**（PartnerContact 模式）：角色*、優先序*、專業領域、備註、業務範圍

**驗收條件**：
- ✅ 所有表單模式正常運作
- ✅ 欄位驗證規則一致
- ✅ 響應式設計適配所有斷點

### FR-002：統一篩選和搜尋功能
**描述**：使用 FilterSearchContainer 統一所有 Contact 相關的篩選和搜尋功能

**需求詳情**：
- 支援文字搜尋：聯絡人姓名、電子郵件、公司、部門
- 支援條件篩選：聯絡人類型、狀態、部門、公司
- 提供篩選結果統計
- 支援清除所有篩選條件

**篩選選項配置**：
```typescript
const contactFilterOptions: FilterOption[] = [
  CommonFilterOptions.textInput("聯絡人姓名", "name"),
  CommonFilterOptions.textInput("電子郵件", "email"),
  CommonFilterOptions.textInput("公司", "company"),
  CommonFilterOptions.textInput("部門", "department"),
  CommonFilterOptions.statusSelect("聯絡人類型", "contactType", CONTACT_TYPES),
  CommonFilterOptions.statusSelect("狀態", "isActive", STATUS_OPTIONS)
];
```

**驗收條件**：
- ✅ 搜尋功能正常運作
- ✅ 篩選功能正常運作
- ✅ 篩選結果統計準確
- ✅ 響應式設計適配

### FR-003：優化 Partner 頁面聯絡人管理
**描述**：統一 Partner 頁面的聯絡人管理流程，消除功能重複

**需求詳情**：
- 使用統一的 ContactFormModal 組件
- 快速新增模式保持高效性
- 自動處理 PartnerContact 關聯
- 簡化關聯管理操作

**操作流程**：
1. 點擊「快速新增聯絡人」
2. 開啟 ContactFormModal（quick 模式）
3. 填寫基本資訊和關聯資訊
4. 自動建立 Contact 和 PartnerContact 記錄
5. 更新 Partner 頁面顯示

**驗收條件**：
- ✅ 快速新增流程順暢
- ✅ 關聯建立正確
- ✅ 操作回饋及時
- ✅ 錯誤處理完善

### FR-004：主頁面聯絡人管理優化
**描述**：優化主頁面的聯絡人管理功能，使用統一的組件和流程

**需求詳情**：
- 使用 FilterSearchContainer 統一篩選
- 使用重構後的 ContactFormModal
- 統一成功和錯誤處理
- 保持與 Partner 頁面的操作一致性

**操作流程**：
1. 使用篩選器搜尋和篩選聯絡人
2. 點擊「新增聯絡人」
3. 開啟 ContactFormModal（create 模式）
4. 填寫完整聯絡人資訊
5. 建立 Contact 記錄
6. 更新列表顯示

**驗收條件**：
- ✅ 篩選功能正常
- ✅ 新增流程順暢
- ✅ 與 Partner 頁面操作一致
- ✅ 資料同步正確

## 非功能需求

### NFR-001：響應式設計
**描述**：所有組件必須支援響應式設計

**需求詳情**：
- **移動端** (≤768px)：簡化佈局，垂直排列
- **平板** (769-1024px)：適中佈局，部分水平排列
- **桌面** (>1024px)：完整佈局，水平排列

**驗收條件**：
- ✅ 所有斷點正常顯示
- ✅ 觸控操作友好
- ✅ 文字大小適中

### NFR-002：效能要求
**描述**：組件載入和操作必須保持高效能

**需求詳情**：
- 表單開啟時間 < 500ms
- 篩選回應時間 < 300ms
- 資料載入時間 < 1s

**驗收條件**：
- ✅ 效能指標達標
- ✅ 無明顯延遲
- ✅ 載入狀態提示

### NFR-003：可用性要求
**描述**：組件必須易於使用和理解

**需求詳情**：
- 操作流程直觀
- 錯誤訊息清晰
- 成功回饋及時
- 鍵盤導航支援

**驗收條件**：
- ✅ 使用者測試通過
- ✅ 無障礙支援
- ✅ 操作回饋完善

### NFR-004：維護性要求
**描述**：代碼必須易於維護和擴展

**需求詳情**：
- 組件重用率 > 90%
- 代碼重複率 < 10%
- TypeScript 覆蓋率 100%
- 文檔完整性 > 95%

**驗收條件**：
- ✅ 代碼品質指標達標
- ✅ 文檔完整準確
- ✅ 易於擴展

## 技術約束

### TC-001：技術棧限制
- 必須使用 React 18 + TypeScript
- 必須使用 Ant Design 5.x 組件
- 必須遵循 FastERP 編碼規範

### TC-002：組件依賴
- 必須使用 IMS 共通組件：FilterSearchContainer、ResponsiveTable、ResponsiveModalConfig
- 必須使用 CommonFilterOptions 模板
- 必須參考 Item 模組設計模式

### TC-003：兼容性要求
- 必須向後兼容現有功能
- 必須支援現有的 API 介面
- 必須保持現有的資料結構

## 使用者故事

### US-001：聯絡人管理員新增聯絡人
**作為** 聯絡人管理員  
**我想要** 使用統一的表單新增聯絡人  
**以便** 確保資料的一致性和完整性  

**驗收條件**：
- 可以選擇完整新增或快速新增模式
- 表單驗證規則一致
- 成功新增後有明確回饋

### US-002：業務人員管理 Partner 聯絡人
**作為** 業務人員  
**我想要** 在 Partner 頁面快速新增和管理聯絡人  
**以便** 高效地維護客戶關係  

**驗收條件**：
- 快速新增流程簡潔高效
- 關聯關係自動建立
- 可以方便地編輯和刪除關聯

### US-003：系統管理員搜尋聯絡人
**作為** 系統管理員  
**我想要** 使用統一的搜尋和篩選功能  
**以便** 快速找到需要的聯絡人資訊  

**驗收條件**：
- 支援多種搜尋條件
- 篩選結果準確
- 可以清除所有篩選條件

## 業務規則

### BR-001：表單驗證規則
- 姓名為必填欄位，最大長度 100 字元
- 電子郵件格式必須正確，最大長度 100 字元
- 電話最大長度 20 字元
- 職位最大長度 50 字元
- 部門最大長度 50 字元
- 公司最大長度 100 字元

### BR-002：PartnerContact 關聯規則
- 每個 Partner 可以有多個聯絡人
- 每個聯絡人可以關聯多個 Partner
- 角色為必填欄位
- 優先序為必填欄位，預設值為 99
- 同一 Partner 下的聯絡人優先序不能重複

### BR-003：資料同步規則
- Contact 資料變更時，相關的 PartnerContact 顯示必須同步更新
- 刪除 Contact 時，相關的 PartnerContact 關聯必須一併處理
- 資料操作必須保持事務一致性

## 測試需求

### 功能測試
- 所有表單模式的新增、編輯功能
- 篩選和搜尋功能
- PartnerContact 關聯管理
- 資料驗證和錯誤處理

### 響應式測試
- 移動端 (≤768px) 顯示和操作
- 平板 (769-1024px) 顯示和操作
- 桌面 (>1024px) 顯示和操作

### 效能測試
- 表單載入時間
- 篩選回應時間
- 大量資料處理

### 兼容性測試
- 現有功能不受影響
- API 介面兼容性
- 資料結構兼容性
