# FastERP 權限需求分類分析報告

## 📋 分析日期
**2025-09-13**

## 🔍 權限系統現狀分析

### 權限檢查機制狀態
- **RequirePermissionAttribute：** ⚠️ 開發環境中被停用（第 26 行 `return;`）
- **細粒度權限服務：** ✅ 已實作並註冊
- **前端權限檢查：** ✅ 使用 PermissionGuard 和 useGranularPermissions
- **配置控制：** ✅ 支援 `GranularPermissions:Enabled` 設定

### 現有控制器權限狀態

#### ✅ 有權限保護的控制器
| 控制器 | 架構類型 | 權限檢查 | 資源路徑 | 狀態 |
|--------|----------|----------|----------|------|
| **ItemController** | 傳統架構 | RequirePermission | `Ims/Item` | 完整保護 |
| **PartnerController** | 傳統架構 | RequirePermission | `Ims/Partner` | 完整保護 |
| **GranularPermissionController** | 傳統架構 | RequirePermission | `Common/GranularPermission` | 完整保護 |

#### ❌ 無權限保護的控制器
| 控制器 | 架構類型 | 權限檢查 | 風險等級 | 建議 |
|--------|----------|----------|----------|------|
| **ContactController** | 泛型架構 | 無 | 🔴 高 | 需要權限保護 |
| **WarehouseController** | 泛型架構 | 無 | 🔴 高 | 需要權限保護 |
| **PartnerAddressController** | 傳統架構 | 無 | 🟡 中 | 需要權限保護 |
| **PartnerContactController** | 傳統架構 | 無 | 🟡 中 | 需要權限保護 |

#### ⚪ 公開或特殊控制器
| 控制器 | 類型 | 說明 | 權限需求 |
|--------|------|------|----------|
| **LoginController** | 認證 | 登入功能 | 無需權限 |
| **CityController** | 基礎資料 | 縣市資料 | 可考慮公開 |
| **DistrictController** | 基礎資料 | 區域資料 | 可考慮公開 |

## 🎯 權限需求分類標準

### 高風險（必須有權限保護）
**標準：**
- 涉及核心業務資料（客戶、商品、訂單等）
- 具有新增、修改、刪除功能
- 包含敏感商業資訊
- 影響系統安全性

**適用控制器：**
- ItemController（庫存品管理）
- PartnerController（商業夥伴管理）
- ContactController（聯絡人管理）
- WarehouseController（倉庫管理）
- OrderController（訂單管理）
- InvoiceController（發票管理）

### 中風險（建議有權限保護）
**標準：**
- 關聯性業務資料
- 主要為查詢功能，少量修改
- 間接影響業務流程

**適用控制器：**
- PartnerAddressController（夥伴地址）
- PartnerContactController（夥伴聯絡人）
- WarehouseItemPriceController（倉庫價格）

### 低風險（可考慮公開）
**標準：**
- 基礎參考資料
- 僅提供查詢功能
- 不包含敏感資訊
- 系統運作必需資料

**適用控制器：**
- CityController（縣市資料）
- DistrictController（區域資料）
- UnitController（單位資料）
- TaxTypeController（稅別資料）

### 無需權限（系統功能）
**標準：**
- 認證相關功能
- 系統健康檢查
- 公開 API 文件

**適用控制器：**
- LoginController（登入認證）
- HealthController（健康檢查）
- SwaggerController（API 文件）

## 🔧 泛型架構權限整合方案

### 方案一：基類權限檢查（推薦）
```csharp
public abstract class GenericController<TEntity, TDto, TService> : ControllerBase
{
    protected readonly string _resourcePath;
    
    protected GenericController(TService service, string entityName, string? resourcePath = null)
    {
        _service = service;
        _entityName = entityName;
        _resourcePath = resourcePath ?? $"Ims/{typeof(TEntity).Name}";
    }
    
    [HttpGet]
    [RequirePermission] // 使用動態資源路徑
    public virtual async Task<IActionResult> Get()
    {
        // 實作
    }
}
```

### 方案二：子類別權限覆寫
```csharp
public class ContactController : GenericController<Contact, ContactDTO, IContactService>
{
    public ContactController(IContactService service) : base(service, "聯絡人") { }
    
    [HttpGet]
    [RequirePermission("Ims/Contact")]
    public override async Task<IActionResult> Get()
    {
        return await base.Get();
    }
}
```

### 方案三：屬性層級權限
```csharp
[RequirePermission("Ims/Contact")]
public class ContactController : GenericController<Contact, ContactDTO, IContactService>
{
    // 類別層級權限，套用到所有方法
}
```

## 📊 風險評估矩陣

### 當前風險狀況
| 風險類型 | 影響程度 | 發生機率 | 風險等級 | 控制器數量 |
|----------|----------|----------|----------|------------|
| **資料洩露** | 高 | 中 | 🔴 高 | 4 個 |
| **未授權修改** | 高 | 中 | 🔴 高 | 4 個 |
| **系統濫用** | 中 | 低 | 🟡 中 | 2 個 |
| **合規問題** | 中 | 高 | 🟡 中 | 全部 |

### 修正後風險狀況
| 風險類型 | 影響程度 | 發生機率 | 風險等級 | 預期改善 |
|----------|----------|----------|----------|----------|
| **資料洩露** | 高 | 低 | 🟢 低 | -75% |
| **未授權修改** | 高 | 低 | 🟢 低 | -75% |
| **系統濫用** | 中 | 低 | 🟢 低 | -50% |
| **合規問題** | 中 | 低 | 🟢 低 | -80% |

## 🎯 建議實施策略

### 立即行動（高優先級）
1. **啟用權限檢查**
   - 移除 RequirePermissionAttribute 中的 `return;` 語句
   - 確認開發環境權限檢查正常運作

2. **泛型控制器權限修正**
   - ContactController 添加權限保護
   - WarehouseController 添加權限保護

### 短期行動（1-2 週）
3. **傳統控制器權限補強**
   - PartnerAddressController 添加權限保護
   - PartnerContactController 添加權限保護

4. **權限資源定義**
   - 建立對應的權限資源記錄
   - 配置角色權限分配

### 中期行動（1 個月）
5. **權限系統優化**
   - 實作泛型控制器權限基類
   - 建立權限檢查最佳實務

6. **測試和驗證**
   - 權限檢查功能測試
   - 安全性滲透測試

## 📋 實施檢查清單

### 技術實施
- [ ] 移除 RequirePermissionAttribute 的停用代碼
- [ ] ContactController 添加權限檢查
- [ ] WarehouseController 添加權限檢查
- [ ] PartnerAddressController 添加權限檢查
- [ ] PartnerContactController 添加權限檢查

### 權限配置
- [ ] 建立 Ims/Contact 權限資源
- [ ] 建立 Ims/Warehouse 權限資源
- [ ] 建立 Ims/PartnerAddress 權限資源
- [ ] 建立 Ims/PartnerContact 權限資源
- [ ] 配置管理員角色權限

### 測試驗證
- [ ] 權限檢查功能測試
- [ ] 未授權存取測試
- [ ] 前端權限同步測試
- [ ] API 回應格式驗證

## 🔄 持續改進建議

### 架構層面
1. **統一權限模式**：建立標準的權限檢查模式
2. **自動化測試**：建立權限檢查的自動化測試
3. **監控機制**：實作權限違規監控和警報

### 開發流程
1. **權限檢查清單**：新控制器開發必須包含權限檢查
2. **代碼審查**：權限相關代碼必須經過安全審查
3. **文件維護**：權限配置文件即時更新

### 安全強化
1. **最小權限原則**：預設拒絕，明確授權
2. **權限審計**：定期檢查權限分配合理性
3. **安全培訓**：開發團隊安全意識培訓
