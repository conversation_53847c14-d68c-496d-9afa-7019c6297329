"use client";

import React, { useState, useEffect } from 'react';
import { 
  Card, 
  Button, 
  Space, 
  Modal, 
  message, 
  Row, 
  Col,
  Radio,
  Divider,
  Typography,
  Toolt<PERSON>,
  Drawer,
  Tabs,
  Badge
} from 'antd';
import { 
  PlusOutlined, 
  UserAddOutlined, 
  ContactsOutlined,
  SettingOutlined,
  InfoCircleOutlined,
  MenuOutlined,
  MobileOutlined,
  DesktopOutlined
} from '@ant-design/icons';

// 響應式組件
import ResponsiveContactFormModal from '../contact/ResponsiveContactFormModal';
import ContactTable from '../contact/ContactTable';
import FilterSearchContainer from '../shared/FilterSearchContainer';

// 上下文和服務
import { useContactContext } from '@/contexts/ContactContext';
import { Contact } from '@/services/ims/ContactService';
import { PartnerContact } from '@/services/ims/partner';
import { useResponsive } from '@/hooks/useResponsive';
import { logger, SYMBOLS, createContextLogger } from '@/utils/logger';

// 創建上下文日誌器
const responsiveContactLogger = createContextLogger({ module: 'ResponsiveContactManagementCenter' });

const { Title, Text } = Typography;
const { TabPane } = Tabs;

// 組件屬性介面
interface ResponsiveContactManagementCenterProps {
  mode: 'standalone' | 'partner-integrated';
  partnerID?: string;
  onContactSelect?: (contact: Contact) => void;
  onContactCreate?: (contact: Contact, partnerContact?: PartnerContact) => void;
  onClose?: () => void;
  readonly?: boolean;
  showModeSelector?: boolean;
  className?: string;
}

// 操作模式類型
type OperationMode = 'browse' | 'create' | 'quick' | 'edit';

const ResponsiveContactManagementCenter: React.FC<ResponsiveContactManagementCenterProps> = ({
  mode,
  partnerID,
  onContactSelect,
  onContactCreate,
  onClose,
  readonly = false,
  showModeSelector = true,
  className = ''
}) => {
  // 響應式 Hook
  const { screenSize, isMobile, isTablet, isDesktop } = useResponsive();
  
  // 上下文
  const { 
    contacts, 
    loading, 
    error, 
    refreshContacts, 
    addContact, 
    updateContact, 
    deleteContact,
    searchContacts 
  } = useContactContext();

  // 本地狀態
  const [operationMode, setOperationMode] = useState<OperationMode>('browse');
  const [showForm, setShowForm] = useState(false);
  const [editingContact, setEditingContact] = useState<Contact | null>(null);
  const [searchKeyword, setSearchKeyword] = useState('');
  const [filteredContacts, setFilteredContacts] = useState<Contact[]>([]);
  const [showMobileDrawer, setShowMobileDrawer] = useState(false);
  const [activeTab, setActiveTab] = useState('contacts');

  // 初始化載入聯絡人
  useEffect(() => {
    refreshContacts();
  }, [refreshContacts]);

  // 搜尋和篩選聯絡人
  useEffect(() => {
    if (searchKeyword.trim()) {
      const filtered = searchContacts(searchKeyword);
      setFilteredContacts(filtered);
    } else {
      setFilteredContacts(contacts);
    }
  }, [contacts, searchKeyword, searchContacts]);

  // 處理快速新增聯絡人並關聯
  const handleQuickAddAndAssociate = async (contactData: Omit<Contact, 'contactID' | 'createTime' | 'updateTime' | 'isDeleted'>, partnerContactData?: PartnerContact) => {
    try {
      responsiveContactLogger.log(SYMBOLS.LOADING, '開始快速新增聯絡人並關聯', { 
        name: contactData.name,
        mode: mode,
        screenSize
      });

      if (mode === 'partner-integrated' && partnerID) {
        // 使用後端 API 一次性完成（需要實作 addContactAndAssociate API）
        const newContact = await addContact(contactData);
        if (newContact) {
          message.success('聯絡人新增成功');
          await refreshContacts();
          onContactCreate?.(newContact, partnerContactData);
          onClose?.();
        }
      } else {
        // 獨立模式：只新增聯絡人
        const newContact = await addContact(contactData);
        if (newContact) {
          message.success('聯絡人新增成功');
          await refreshContacts();
          onContactCreate?.(newContact);
          onClose?.();
        }
      }
    } catch (error) {
      responsiveContactLogger.log(SYMBOLS.ERROR, '快速新增聯絡人並關聯失敗', error);
      message.error('操作失敗：' + (error instanceof Error ? error.message : '未知錯誤'));
    }
  };

  // 處理聯絡人選擇
  const handleContactSelect = (contact: Contact) => {
    responsiveContactLogger.log(SYMBOLS.SUCCESS, '聯絡人已選擇', { contactId: contact.contactID });
    onContactSelect?.(contact);
    
    // 移動端選擇後關閉抽屜
    if (isMobile) {
      setShowMobileDrawer(false);
    }
  };

  // 處理聯絡人編輯
  const handleContactEdit = (contact: Contact) => {
    responsiveContactLogger.log(SYMBOLS.SUCCESS, '開始編輯聯絡人', { contactId: contact.contactID });
    setEditingContact(contact);
    setOperationMode('edit');
    setShowForm(true);
  };

  // 處理聯絡人刪除
  const handleContactDelete = async (contactId: string) => {
    const success = await deleteContact(contactId);
    if (success) {
      await refreshContacts();
    }
  };

  // 開啟新增模式
  const openCreateMode = () => {
    setEditingContact(null);
    setOperationMode('create');
    setShowForm(true);
  };

  // 開啟快速新增模式
  const openQuickMode = () => {
    setEditingContact(null);
    setOperationMode('quick');
    setShowForm(true);
  };

  // 關閉表單
  const handleFormClose = () => {
    setShowForm(false);
    setEditingContact(null);
    setOperationMode('browse');
  };

  // 處理表單提交
  const handleFormSubmit = async (contactData: Contact) => {
    try {
      if (operationMode === 'edit' && editingContact) {
        // 編輯模式
        const success = await updateContact(contactData);
        if (success) {
          await refreshContacts();
          handleFormClose();
        }
      } else if (operationMode === 'create') {
        // 新增模式
        const newContact = await addContact(contactData);
        if (newContact) {
          await refreshContacts();
          handleFormClose();
        }
      } else if (operationMode === 'quick') {
        // 快速新增模式
        await handleQuickAddAndAssociate(contactData);
        handleFormClose();
      }
    } catch (error) {
      responsiveContactLogger.log(SYMBOLS.ERROR, '表單提交失敗', error);
    }
  };

  // 渲染操作按鈕
  const renderActionButtons = () => {
    if (readonly) return null;

    const buttons = [
      <Button
        key="add"
        type="primary"
        icon={<PlusOutlined />}
        onClick={openCreateMode}
        size={isMobile ? 'small' : 'middle'}
      >
        {isMobile ? '新增' : '新增聯絡人'}
      </Button>
    ];

    if (mode === 'partner-integrated') {
      buttons.push(
        <Button
          key="quick"
          type="default"
          icon={<UserAddOutlined />}
          onClick={openQuickMode}
          size={isMobile ? 'small' : 'middle'}
        >
          {isMobile ? '快速關聯' : '快速新增並關聯'}
        </Button>
      );
    }

    return <Space wrap>{buttons}</Space>;
  };

  // 渲染模式選擇器
  const renderModeSelector = () => {
    if (!showModeSelector) return null;

    return (
      <Card size="small" style={{ marginBottom: 16 }}>
        <Row align="middle" justify="space-between">
          <Col>
            <Space>
              <ContactsOutlined />
              <Text strong>操作模式</Text>
              <Tooltip title="選擇適合的操作模式來管理聯絡人">
                <InfoCircleOutlined style={{ color: '#1890ff' }} />
              </Tooltip>
            </Space>
          </Col>
          <Col>
            <Radio.Group 
              value={operationMode} 
              onChange={(e) => setOperationMode(e.target.value)}
              buttonStyle="solid"
              size={isMobile ? 'small' : 'middle'}
            >
              <Radio.Button value="browse">瀏覽</Radio.Button>
              <Radio.Button value="create">新增</Radio.Button>
              {mode === 'partner-integrated' && (
                <Radio.Button value="quick">快速關聯</Radio.Button>
              )}
            </Radio.Group>
          </Col>
        </Row>
      </Card>
    );
  };

  // 渲染聯絡人表格
  const renderContactTable = () => {
    return (
      <ContactTable
        contacts={filteredContacts}
        loading={loading}
        onEdit={handleContactEdit}
        onDelete={handleContactDelete}
        readonly={readonly}
        showActions={!readonly}
        pagination={isMobile ? false : {
          pageSize: isMobile ? 5 : 10,
          showSizeChanger: !isMobile,
          showQuickJumper: !isMobile,
          showTotal: (total: number, range: [number, number]) =>
            isMobile ? `${range[0]}-${range[1]}/${total}` : `第 ${range[0]}-${range[1]} 項，共 ${total} 項`
        }}
      />
    );
  };

  // 渲染搜尋和篩選
  const renderSearchAndFilter = () => {
    return (
      <FilterSearchContainer
        title="篩選與搜尋"
        searchPlaceholder={isMobile ? "搜尋聯絡人" : "搜尋聯絡人（姓名、公司、職位、信箱）"}
        filterOptions={[
          { label: '聯絡人類型', value: 'contactType', type: 'select', children: [
            { label: '內部員工', value: 'internal' },
            { label: '外部聯絡人', value: 'external' },
            { label: '客戶', value: 'customer' },
            { label: '供應商', value: 'supplier' },
            { label: '合作夥伴', value: 'partner' }
          ]}
        ]}
        onFilterResult={(state) => {
          setSearchKeyword(state.searchText || '');
          const list = state.searchText?.trim()
            ? searchContacts(state.searchText)
            : contacts;
          const filtered = list.filter(c => {
            if (state.filterValues.contactType && state.filterValues.contactType.length > 0) {
              if (!state.filterValues.contactType.includes(c.contactType)) return false;
            }
            return true;
          });
          setFilteredContacts(filtered);
        }}
        className="contact-filter-container"
      />
    );
  };

  // 渲染表單 Modal
  const renderFormModal = () => {
    if (!showForm) return null;

    return (
      <ResponsiveContactFormModal
        visible={showForm}
        mode={operationMode === 'quick' ? 'quick' : operationMode === 'edit' ? 'edit' : 'create'}
        contact={editingContact}
        partnerID={partnerID}
        onClose={handleFormClose}
        onSubmit={handleFormSubmit}
        title={
          operationMode === 'edit' ? '編輯聯絡人' :
          operationMode === 'quick' ? '快速新增並關聯' :
          '新增聯絡人'
        }
      />
    );
  };

  // 移動端抽屜內容
  const renderMobileDrawerContent = () => {
    return (
      <div>
        <div style={{ marginBottom: 16 }}>
          <Space>
            <MobileOutlined />
            <Text strong>聯絡人管理</Text>
            <Badge count={filteredContacts.length} />
          </Space>
        </div>
        
        {renderSearchAndFilter()}
        {renderContactTable()}
      </div>
    );
  };

  // 桌面端內容
  const renderDesktopContent = () => {
    return (
      <div className={`contact-management-center ${className}`}>
        {/* 模式選擇器 */}
        {renderModeSelector()}

        {/* 搜尋和篩選 */}
        {renderSearchAndFilter()}

        {/* 聯絡人管理卡片 */}
        <Card
          title={
            <Space>
              <ContactsOutlined />
              <span>聯絡人管理</span>
              <Text type="secondary">({filteredContacts.length} 個聯絡人)</Text>
            </Space>
          }
          extra={renderActionButtons()}
          loading={loading}
        >
          {/* 錯誤訊息 */}
          {error && (
            <div style={{ marginBottom: 16, padding: 12, background: '#fff2f0', border: '1px solid #ffccc7', borderRadius: 6 }}>
              <Text type="danger">{error}</Text>
            </div>
          )}

          {/* 聯絡人表格 */}
          {renderContactTable()}
        </Card>

        {/* 表單 Modal */}
        {renderFormModal()}
      </div>
    );
  };

  // 根據螢幕大小渲染不同佈局
  if (isMobile) {
    return (
      <div className={`responsive-contact-management ${className}`}>
        {/* 移動端操作按鈕 */}
        <Card size="small" style={{ marginBottom: 16 }}>
          <Row justify="space-between" align="middle">
            <Col>
              <Space>
                <ContactsOutlined />
                <Text strong>聯絡人管理</Text>
                <Badge count={filteredContacts.length} />
              </Space>
            </Col>
            <Col>
              <Space>
                {renderActionButtons()}
                <Button
                  icon={<MenuOutlined />}
                  onClick={() => setShowMobileDrawer(true)}
                >
                  瀏覽
                </Button>
              </Space>
            </Col>
          </Row>
        </Card>

        {/* 移動端抽屜 */}
        <Drawer
          title="聯絡人列表"
          placement="bottom"
          height="80%"
          open={showMobileDrawer}
          onClose={() => setShowMobileDrawer(false)}
          extra={
            <Button type="text" onClick={() => setShowMobileDrawer(false)}>
              關閉
            </Button>
          }
        >
          {renderMobileDrawerContent()}
        </Drawer>

        {/* 表單 Modal */}
        {renderFormModal()}
      </div>
    );
  }

  // 桌面端和平板端
  return renderDesktopContent();
};

export default ResponsiveContactManagementCenter;
