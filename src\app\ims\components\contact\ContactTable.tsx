"use client";

import React from 'react';
import { 
  Tag, 
  Avatar, 
  Space, 
  Typography,
  Button,
  Tooltip,
  Popconfirm,
  message
} from 'antd';
import { 
  UserOutlined, 
  PhoneOutlined, 
  MailOutlined,
  EditOutlined, 
  DeleteOutlined
} from '@ant-design/icons';

import { Contact } from '@/services/ims/ContactService';
import ResponsiveTable from '@/app/ims/components/shared/ResponsiveTable';
import type { ResponsiveTableProps } from '@/app/ims/components/shared/ResponsiveTable';
import { logger, SYMBOLS, createContextLogger } from '@/utils/logger';

// 創建上下文日誌器
const contactTableLogger = createContextLogger({ module: 'ContactTable' });

const { Text, Title } = Typography;

// 組件屬性介面
export interface ContactTableProps extends Omit<ResponsiveTableProps<Contact>, 'columns' | 'statusColorMap' | 'typeColorMap' | 'avatarRender'> {
  contacts: Contact[];
  onEdit?: (contact: Contact) => void;
  onDelete?: (contactId: string) => void;
  readonly?: boolean;
  showActions?: boolean;
}

// 聯絡人狀態標籤顏色映射
const getStatusColor = (isActive: boolean) => {
  return isActive ? 'green' : 'red';
};

// 聯絡人類型標籤顏色映射
const getTypeColor = (type: string) => {
  const typeColors: { [key: string]: string } = {
    'internal': 'blue',
    'external': 'orange',
    'customer': 'green',
    'supplier': 'purple',
    'partner': 'cyan'
  };
  return typeColors[type] || 'default';
};

const ContactTable: React.FC<ContactTableProps> = ({
  contacts,
  onEdit,
  onDelete,
  readonly = false,
  showActions = true,
  ...tableProps
}) => {
  // 處理聯絡人刪除
  const handleContactDelete = async (contactId: string) => {
    try {
      contactTableLogger.log(SYMBOLS.LOADING, '開始刪除聯絡人', { contactId });
      await onDelete?.(contactId);
      message.success('聯絡人刪除成功');
    } catch (error) {
      contactTableLogger.log(SYMBOLS.ERROR, '刪除聯絡人失敗', error);
      message.error('刪除失敗：' + (error instanceof Error ? error.message : '未知錯誤'));
    }
  };

  // 渲染聯絡人頭像
  const renderAvatar = (contact: Contact) => {
    return (
      <Avatar 
        size={48} 
        icon={<UserOutlined />}
        style={{ 
          backgroundColor: contact.isActive ? '#52c41a' : '#ff4d4f',
          color: 'white'
        }}
      />
    );
  };

  // 狀態顏色映射
  const statusColorMap = (contact: Contact) => ({
    isActive: getStatusColor(contact.isActive)
  });

  // 類型顏色映射
  const typeColorMap = (contact: Contact) => ({
    contactType: getTypeColor(contact.contactType)
  });

  // 桌面端表格列定義
  const columns = [
    {
      title: '聯絡人',
      key: 'contact',
      width: 250,
      render: (record: Contact) => (
        <Space>
          {renderAvatar(record)}
          <div>
            <div style={{ fontWeight: 500, marginBottom: 4 }}>
              {record.name}
            </div>
            <div style={{ fontSize: '12px', color: '#666' }}>
              {record.position && `${record.position}`}
              {record.company && ` • ${record.company}`}
            </div>
          </div>
        </Space>
      ),
    },
    {
      title: '聯絡方式',
      key: 'contactInfo',
      width: 250,
      render: (record: Contact) => (
        <Space direction="vertical" size={2}>
          {record.email && (
            <Space size={4}>
              <MailOutlined style={{ color: '#1890ff', fontSize: '12px' }} />
              <Text style={{ fontSize: '12px' }}>{record.email}</Text>
            </Space>
          )}
          {record.phone && (
            <Space size={4}>
              <PhoneOutlined style={{ color: '#52c41a', fontSize: '12px' }} />
              <Text style={{ fontSize: '12px' }}>{record.phone}</Text>
            </Space>
          )}
        </Space>
      ),
    },
    {
      title: '狀態',
      key: 'status',
      width: 120,
      render: (record: Contact) => (
        <Space direction="vertical" size={4}>
          <Tag color={getStatusColor(record.isActive)}>
            {record.isActive ? '啟用' : '停用'}
          </Tag>
          <Tag color={getTypeColor(record.contactType)}>
            {record.contactType}
          </Tag>
        </Space>
      ),
    },
    {
      title: '部門',
      key: 'department',
      width: 100,
      render: (record: Contact) => (
        record.department ? (
          <Tag color="blue">{record.department}</Tag>
        ) : (
          <Text type="secondary">-</Text>
        )
      ),
    },
    ...(showActions && !readonly ? [{
      title: '操作',
      key: 'actions',
      width: 120,
      render: (record: Contact) => (
        <Space>
          <Tooltip title="編輯">
            <Button 
              type="text" 
              icon={<EditOutlined />} 
              onClick={() => onEdit?.(record)}
            />
          </Tooltip>
          <Popconfirm
            title="確定要刪除這個聯絡人嗎？"
            onConfirm={() => handleContactDelete(record.contactID)}
            okText="確定"
            cancelText="取消"
          >
            <Button 
              type="text" 
              danger 
              icon={<DeleteOutlined />}
            />
          </Popconfirm>
        </Space>
      ),
    }] : []),
  ];

  // 自定義移動端卡片渲染
  const mobileCardRender = ({ record, actions }: { record: Contact; actions?: React.ReactNode }) => (
    <div>
      <div style={{ display: 'flex', alignItems: 'center', gap: 12, marginBottom: 12 }}>
        {renderAvatar(record)}
        <div style={{ flex: 1 }}>
          <Title level={5} style={{ margin: 0, marginBottom: 4 }}>
            {record.name}
          </Title>
          <Space wrap size={[4, 4]}>
            <Tag color={getStatusColor(record.isActive)}>
              {record.isActive ? '啟用' : '停用'}
            </Tag>
            <Tag color={getTypeColor(record.contactType)}>
              {record.contactType}
            </Tag>
            {record.department && (
              <Tag color="blue">{record.department}</Tag>
            )}
          </Space>
        </div>
      </div>
      
      <Space direction="vertical" size={4} style={{ width: '100%' }}>
        {record.position && (
          <div>
            <Text type="secondary" style={{ fontSize: '12px' }}>職位：</Text>
            <Text style={{ fontSize: '12px' }}>{record.position}</Text>
          </div>
        )}
        {record.company && (
          <div>
            <Text type="secondary" style={{ fontSize: '12px' }}>公司：</Text>
            <Text style={{ fontSize: '12px' }}>{record.company}</Text>
          </div>
        )}
        {record.email && (
          <div>
            <Space size={4}>
              <MailOutlined style={{ color: '#1890ff', fontSize: '12px' }} />
              <Text style={{ fontSize: '12px' }}>{record.email}</Text>
            </Space>
          </div>
        )}
        {record.phone && (
          <div>
            <Space size={4}>
              <PhoneOutlined style={{ color: '#52c41a', fontSize: '12px' }} />
              <Text style={{ fontSize: '12px' }}>{record.phone}</Text>
            </Space>
          </div>
        )}
      </Space>
    </div>
  );

  return (
    <ResponsiveTable<Contact>
      columns={columns}
      dataSource={contacts}
      rowKey="contactID"
      statusColorMap={statusColorMap}
      typeColorMap={typeColorMap}
      avatarRender={renderAvatar}
      mobileCardRender={mobileCardRender}
      {...tableProps}
    />
  );
};

export default ContactTable;
