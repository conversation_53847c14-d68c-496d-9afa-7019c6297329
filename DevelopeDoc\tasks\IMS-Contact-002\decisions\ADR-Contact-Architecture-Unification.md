# ADR-Contact-Architecture-Unification：聯絡人管理架構統一決策

## 狀態
已接受

## 決策日期
2025-09-12

## 背景
聯絡人管理功能在系統中分散在多個組件中，存在以下問題：
1. 選項定義重複（聯絡人類型、角色、狀態）
2. 驗證規則重複且不一致
3. 表單邏輯重複（快速新增 vs 完整新增）
4. 搜尋篩選邏輯重複
5. 資料處理邏輯重複（正規化、後援查找）

這導致維護困難、使用者體驗不一致，以及開發效率低下。

## 決策
我們決定採用以下架構來統一聯絡人管理：

### 1. 充分利用現有系統級工具
**決策**：最大化重用現有的共享組件和工具，而不是重新發明輪子。

**理由**：
- 系統已有成熟的 `AdvancedFilterComponent`、`ResponsiveTable`、`FilterSearchContainer` 等組件
- 後端已有 `ValidationHelper.cs`、`StringHelper.cs` 等工具類別
- 重用現有工具可以確保一致性並減少維護成本

**具體實作**：
```
後端工具層（現有）：
├── ValidationHelper.cs     # 驗證邏輯
├── StringHelper.cs         # 字串處理  
├── BusinessHelper.cs       # 業務邏輯
└── ToolManager.cs          # 統一工具管理

前端共享基礎設施層（現有）：
├── AdvancedFilterComponent # 篩選器
├── ResponsiveTable         # 響應式表格
├── FilterSearchContainer   # 篩選搜尋容器
├── ResponsiveModalConfig   # 響應式模態框
└── ResponsiveStyles        # 統一樣式

聯絡人專用層（新增）：
├── contactConstants.ts     # 常數定義
├── contactValidation.ts    # 驗證規則（對應後端）
└── contactUtils.ts         # 工具函數
```

### 2. 單一資料來源原則
**決策**：所有聯絡人相關的常數、選項、驗證規則都統一定義在專用檔案中。

**理由**：
- 避免重複定義導致的不一致
- 便於維護和擴展
- 確保前後端驗證規則同步

**具體實作**：
- `contactConstants.ts`：所有選項和常數
- `contactValidation.ts`：所有驗證規則，對應後端 `ValidationHelper.cs`
- `contactUtils.ts`：所有工具函數，包括正規化、搜尋、篩選邏輯

### 3. 組件職責分離
**決策**：建立純表單欄位組件，由業務組件組合使用。

**理由**：
- 提高組件的可重用性
- 便於測試和維護
- 支援不同的使用場景（快速新增、完整編輯、唯讀檢視）

**具體實作**：
```typescript
// ContactFormFields.tsx - 純表單欄位組件
interface ContactFormFieldsProps {
  mode?: 'quick' | 'full' | 'readonly';
  initialValues?: Partial<Contact>;
  onChange?: (values: Partial<Contact>) => void;
  isMobile?: boolean;
}

// ContactFormModal.tsx - 業務組件，組合 ContactFormFields
// ContactManagementTab.tsx - 業務組件，使用 ContactFormFields
```

### 4. 漸進式重構策略
**決策**：分階段重構，每個階段都確保系統可正常運作。

**理由**：
- 降低重構風險
- 便於測試和驗證
- 可以根據進度調整計畫

**具體階段**：
1. 建立共享基礎設施
2. 重構現有組件
3. 整合測試與優化

## 替代方案

### 替代方案 1：完全重寫聯絡人管理
**優點**：可以設計最優架構
**缺點**：風險高、工期長、可能影響現有功能
**為何拒絕**：風險太高，且現有組件基本功能正常

### 替代方案 2：僅修復當前問題
**優點**：風險低、工期短
**缺點**：不能根本解決架構問題，未來還會遇到相同問題
**為何拒絕**：治標不治本，長期維護成本更高

### 替代方案 3：建立全新的共享組件系統
**優點**：可以設計完美的組件架構
**缺點**：重複造輪子，與現有系統不一致
**為何拒絕**：系統已有優秀的共享組件，應該充分利用

## 後果

### 正面影響
1. **維護效率提升**：修改選項或驗證規則只需更新一處
2. **使用者體驗一致**：所有聯絡人功能具有統一行為
3. **開發效率提升**：新功能可直接重用共享組件
4. **程式碼品質提升**：減少重複代碼，提高可測試性
5. **架構清晰度提升**：明確的分層和職責劃分

### 可能的負面影響
1. **短期開發成本**：需要重構現有組件
2. **測試複雜度**：需要確保重構不影響現有功能
3. **學習成本**：開發人員需要熟悉新的架構

### 緩解措施
1. **分階段實作**：降低重構風險
2. **完整測試**：確保功能正確性
3. **文件完善**：降低學習成本
4. **程式碼審查**：確保實作品質

## 實作要點

### 必須遵循的原則
1. **向後相容**：不改變現有 API 和使用者操作流程
2. **效能不降低**：重構後效能應保持或提升
3. **響應式設計**：確保在各種裝置上正常運作
4. **無障礙支援**：保持現有的無障礙功能

### 關鍵實作細節
1. **常數定義**：使用 TypeScript 的 `const assertions` 確保型別安全
2. **驗證規則**：與後端 `ValidationHelper.cs` 保持同步
3. **工具函數**：使用純函數，便於測試
4. **組件設計**：支援受控和非受控模式

### 品質保證
1. **單元測試**：所有工具函數都要有測試
2. **整合測試**：確保組件間正確協作
3. **回歸測試**：確保現有功能不受影響
4. **效能測試**：確保重構後效能符合要求

## 相關決策
- 本決策與 [ADR-Partner-Contacts-Normalize](../../IMS-Partner-004/decisions/ADR-Partner-Contacts-Normalize.md) 相關
- 本決策遵循 [架構一致性準則](../../../DEVELOPMENT_GUIDELINES.md#7-架構一致性準則)

## 參考資料
- [DEVELOPMENT_GUIDELINES.md](../../../DEVELOPMENT_GUIDELINES.md)
- [現有共享組件文件](../../../../fast_erp_frontend/src/app/ims/components/shared/)
- [後端工具類別](../../../../Tools/)

---
**決策者**：開發團隊  
**審查者**：架構師  
**實作負責人**：前端開發團隊
