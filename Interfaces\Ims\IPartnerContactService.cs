using FAST_ERP_Backend.Models.Ims;

namespace FAST_ERP_Backend.Interfaces.Ims;

/// <summary> 商業夥伴聯絡人服務介面 </summary>
public interface IPartnerContactService
{
    /// <summary>
    /// 取得指定商業夥伴的所有聯絡人關聯清單。
    /// </summary>
    /// <param name="PartnerID">商業夥伴 ID</param>
    /// <returns>聯絡人關聯 DTO 清單</returns>
    Task<List<PartnerContactDTO>> GetAsync(Guid PartnerID);
    
    /// <summary>
    /// 取得指定商業夥伴與聯絡人的單一關聯詳情（複合鍵）。
    /// </summary>
    /// <param name="PartnerID">商業夥伴 ID</param>
    /// <param name="ContactID">聯絡人 ID</param>
    /// <returns>聯絡人關聯 DTO</returns>
    Task<PartnerContactDTO> GetAsync(Guid PartnerID, Guid ContactID);
    
    /// <summary>
    /// 根據單一關聯 ID 取得聯絡人關聯詳情。
    /// </summary>
    /// <param name="partnerContactId">聯絡人關聯 ID</param>
    /// <returns>聯絡人關聯 DTO</returns>
    Task<PartnerContactDTO> GetByIdAsync(Guid partnerContactId);
    
    /// <summary>
    /// 直接新增商業夥伴與聯絡人的關聯（僅關聯資料）。
    /// </summary>
    /// <param name="dto">關聯資料 DTO</param>
    /// <returns>操作是否成功與訊息</returns>
    Task<(bool, string)> AddAsync(PartnerContactDTO dto);
    
    /// <summary>
    /// 更新商業夥伴與聯絡人的關聯資料（角色、優先序等）。
    /// </summary>
    /// <param name="dto">關聯資料 DTO</param>
    /// <returns>操作是否成功與訊息</returns>
    Task<(bool, string)> UpdateAsync(PartnerContactDTO dto);
    
    /// <summary>
    /// 依商業夥伴與聯絡人複合鍵刪除單一關聯（硬刪除）。
    /// </summary>
    /// <param name="PartnerID">商業夥伴 ID</param>
    /// <param name="ContactID">聯絡人 ID</param>
    /// <returns>操作是否成功與訊息</returns>
    Task<(bool, string)> DeleteAsync(Guid PartnerID, Guid ContactID);
    
    /// <summary>
    /// 將既有聯絡人關聯到指定商業夥伴。
    /// </summary>
    /// <param name="request">關聯請求資料</param>
    /// <returns>操作是否成功與訊息</returns>
    Task<(bool, string)> AssociateAsync(AssociateContactRequest request);
    
    /// <summary>
    /// 依單一關聯 ID 更新聯絡人與夥伴的關聯資訊。
    /// </summary>
    /// <param name="request">更新請求資料</param>
    /// <returns>操作是否成功與訊息</returns>
    Task<(bool, string)> UpdateAssociationAsync(UpdateContactAssociationRequest request);
    
    /// <summary>
    /// 依單一關聯 ID 取消聯絡人與夥伴的關聯（硬刪除）。
    /// </summary>
    /// <param name="partnerContactId">聯絡人關聯 ID</param>
    /// <returns>操作是否成功與訊息</returns>
    Task<(bool, string)> UnassociateAsync(Guid partnerContactId);
    
    /// <summary>
    /// 於交易中一次性新增聯絡人並建立與商業夥伴的關聯。
    /// </summary>
    /// <param name="request">新增與關聯請求資料</param>
    /// <returns>操作是否成功與訊息</returns>
    Task<(bool, string)> AddContactAndAssociateAsync(AddContactAndAssociateRequest request);
} 