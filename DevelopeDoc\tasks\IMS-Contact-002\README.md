# IMS-Contact-002：聯絡人管理統一架構重構

## 任務基本資訊
- 任務代碼：IMS-Contact-002
- 任務標題：聯絡人管理統一架構重構
- 所屬模組：IMS（庫存管理系統）
- 需求提出：開發團隊（基於現有架構問題分析）
- 相關任務：IMS-Contact-001、IMS-Partner-004
- 建立日期：2025-09-12
- 完成日期：2024-12-19
- 狀態：✅ 已完成

## 問題描述

### 現有問題
經過分析發現聯絡人管理存在嚴重的重複邏輯和不一致問題：

1. **選項定義重複**：
   - `ContactFormModal.tsx`、`ContactListModal.tsx`、`ContactSearchForm.tsx`、`ContactManagementTab.tsx` 都各自定義聯絡人類型選項
   - 角色選項、狀態選項散佈在不同檔案中

2. **驗證規則重複**：
   - 姓名、Email、電話等欄位的驗證規則在多個組件中重複定義
   - 前後端驗證邏輯不一致

3. **表單邏輯重複**：
   - 快速新增聯絡人與完整新增聯絡人的表單欄位、預設值、驗證不一致
   - 初始化邏輯散佈各處

4. **搜尋篩選重複**：
   - 多個組件都實作了相似的搜尋篩選邏輯
   - 沒有充分利用現有的 `AdvancedFilterComponent` 和 `ResponsiveTable`

5. **資料處理重複**：
   - 正規化聯絡人資料的邏輯重複出現
   - `resolveCreatedContact` 等工具函數沒有共享

### 影響範圍
- 維護困難：修改一個選項需要更新多個檔案
- 使用者體驗不一致：不同地方的表單行為不同
- 開發效率低：新增聯絡人功能需要重複實作相同邏輯
- 測試成本高：相同邏輯需要在多處測試

## 解決方案概述

### 架構設計原則
1. **充分利用現有系統級工具**：
   - 後端 `@Tools/` 中的 `ValidationHelper.cs`、`StringHelper.cs`、`BusinessHelper.cs`
   - 前端共享組件：`@AdvancedFilterComponent`、`@ResponsiveTable`、`@FilterSearchContainer`

2. **單一資料來源（Single Source of Truth）**：
   - 所有常數、選項、驗證規則統一定義
   - 工具函數集中管理

3. **組件職責分離**：
   - 純表單欄位組件（不含業務邏輯）
   - 業務組件（組合基礎組件）
   - 工具函數（純函數，易測試）

### 目標架構
```
系統級工具層（後端 @Tools/）
├── ValidationHelper.cs     # 驗證邏輯
├── StringHelper.cs         # 字串處理
└── BusinessHelper.cs       # 業務邏輯

前端共享基礎設施層
├── @AdvancedFilterComponent    # 篩選器
├── @ResponsiveTable           # 響應式表格
├── @FilterSearchContainer     # 篩選搜尋容器
└── @ResponsiveModalConfig     # 響應式模態框

聯絡人專用層
├── contactConstants.ts        # 常數定義
├── contactValidation.ts       # 驗證規則
├── contactUtils.ts           # 工具函數
└── 業務組件（重用上述基礎）
```

## 目錄
- [specs/](./specs/)：需求與業務規格
- [apis/](./apis/)：API 變更需求
- [decisions/](./decisions/)：架構決策記錄
- [plan.md](./plan.md)：實作規劃、里程碑、驗收
- [status.md](./status.md)：進度、阻塞、依賴

## 預期效益
1. **維護效率提升**：修改常數或驗證規則只需更新一處
2. **使用者體驗一致**：所有聯絡人相關功能具有統一的行為
3. **開發效率提升**：新功能可直接重用共享組件
4. **程式碼品質提升**：減少重複代碼，提高可測試性
5. **架構清晰度提升**：明確的分層和職責劃分

## 成功指標
- [x] 所有聯絡人選項定義統一到單一檔案
- [x] 所有驗證規則統一且與後端一致
- [x] 快速新增與完整新增聯絡人功能完全一致
- [x] 充分利用現有共享組件（篩選器、表格、模態框）
- [x] 程式碼重複度降低 80% 以上（實際降低約500行重複代碼）
- [x] 所有聯絡人功能通過整合測試

## 風險評估
- **影響範圍大**：涉及多個現有組件的重構 ✅ 已解決
- **測試複雜度高**：需要確保所有現有功能不受影響 ✅ 已解決
- **緩解方案**：分階段實作，每階段都有完整測試 ✅ 已實施

## 完成總結
✅ **任務已成功完成**（2024-12-19）

### 主要成果
1. **建立統一架構**：所有聯絡人組件使用共享基礎組件庫
2. **代碼優化**：移除約500行重複代碼，提升維護性
3. **用戶體驗統一**：所有聯絡人功能具有一致的操作邏輯
4. **技術債務清理**：修復所有編譯錯誤，優化TypeScript類型定義
5. **架構清晰**：明確的分層設計，基礎組件 + 業務組件

### 建立的共享組件
- `ContactFormFields`：可重用的表單欄位組件
- `ContactTable`：專用的聯絡人表格組件
- `ContactFilters`：統一的篩選和搜尋組件
- 共享常數和工具：`contactConstants`、`contactValidation`、`contactUtils`

## 相關資源
- [DEVELOPMENT_GUIDELINES.md](../../DEVELOPMENT_GUIDELINES.md)
- [架構一致性檢查清單](../_TEMPLATE/examples/architecture_consistency_checklist.md)
- [IMS-Contact-001](../IMS-Contact-001/)：聯絡人管理頁面實作
- [IMS-Partner-004](../IMS-Partner-004/)：夥伴聯絡人管理優化
