"use client";

import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { 
  Table, 
  Card, 
  Button, 
  Space, 
  Typography, 
  Descriptions, 
  Input, 
  Divider,
  Checkbox
} from 'antd';
import { SearchOutlined, ClearOutlined, CheckOutlined } from '@ant-design/icons';
import type { TableProps, ColumnsType, ColumnType, TablePaginationConfig } from 'antd/es/table';
import type { FilterValue, SorterResult } from 'antd/es/table/interface';

// 導入共享的 API 類型
import { QueryState, ServerQueryResponse } from '@/app/ims/types/filter';

/**
 * 響應式表格組件
 * 
 * 自動在移動端切換為卡片列表模式，在桌面端使用標準表格模式。
 * 提供統一的分頁處理和響應式行為，簡化新頁面開發。
 * 
 * 特性：
 * - 自動檢測移動端 (≤768px) 並切換為卡片模式
 * - 統一的分頁邏輯，支援移動端和桌面端
 * - 可自定義移動端卡片渲染
 * - 智能篩選器：支援個別欄位配置篩選器
 * - 動態篩選器：資料量大時自動顯示統一篩選界面
 * - 保持所有 Antd Table 的原有功能
 * - 支援 TypeScript 泛型
 * 
 * @example
 * ```tsx
 * // 基本使用
 * <ResponsiveTable
 *   columns={columns}
 *   dataSource={data}
 *   rowKey="id"
 * />
 * 
 * // 欄位級智能篩選器
 * const columns = [
 *   {
 *     title: '名稱',
 *     dataIndex: 'name',
 *     enableSmartFilter: true,        // 啟用智能篩選器
 *     filterType: 'input',            // 強制使用輸入框
 *     filterPlaceholder: '搜尋名稱'    // 自定義 placeholder
 *   },
 *   {
 *     title: '狀態',
 *     dataIndex: 'status',
 *     enableSmartFilter: true,        // 啟用智能篩選器
 *     filterType: 'select'            // 強制使用下拉選單
 *   }
 * ];
 * 
 * <ResponsiveTable
 *   columns={columns}
 *   dataSource={data}
 *   rowKey="id"
 *   enableDynamicFilter={true}        // 啟用動態篩選器
 *   filterThreshold={10}              // 資料量超過10筆時顯示篩選器
 * />
 * ```
 * 
 * <AUTHOR> Team
 * @version 1.2.0
 */

export interface MobileCardRenderProps<T = any> {
  /** 當前記錄數據 */
  record: T;
  /** 記錄索引 */
  index: number;
  /** 操作按鈕組件 */
  actions?: React.ReactNode;
}

export interface SmartColumnType<T = any> extends ColumnType<T> {
  /** 是否啟用智能篩選器 */
  smartFilter?: boolean;
  /** 篩選器類型：'text' | 'select' | 'select-search' | 'auto' */
  filterType?: 'text' | 'select' | 'select-search' | 'auto';
  /** 自定義篩選器 placeholder */
  filterPlaceholder?: string;
  /** 對應欄位值的顯示文字（例如把 GUID 轉成名稱） */
  filterDisplayValue?: (value: any, record?: T) => string;
}

export interface ResponsiveTableProps<T = any> extends Omit<TableProps<T>, 'pagination'> {
  /** 移動端卡片渲染函數 */
  mobileCardRender?: (props: MobileCardRenderProps<T>) => React.ReactNode;
  /** 移動端每頁顯示數量 */
  mobilePageSize?: number;
  /** 是否在移動端顯示分頁 */
  showMobilePagination?: boolean;
  /** 移動端斷點 */
  mobileBreakpoint?: number;
  /** 分頁配置 */
  pagination?: TableProps<T>['pagination'] | false;
  /** 移動端卡片額外樣式 */
  mobileCardStyle?: React.CSSProperties;
  /** 移動端卡片額外類名 */
  mobileCardClassName?: string;
  /** 是否啟用動態篩選器 */
  enableDynamicFilter?: boolean;
  /** 動態篩選器顯示閾值 */
  filterThreshold?: number;
  /** 動態篩選器配置 */
  filterConfigs?: Array<{
    dataIndex: string;
    title: string;
    placeholder?: string;
    enabled?: boolean;
  }>;

  // 伺服器模式支援
  /** 是否啟用伺服器模式（分頁/篩選/排序都在後端處理） */
  serverMode?: boolean;
  /** 伺服器請求回調函數 */
  onServerRequest?: (queryState: QueryState) => Promise<ServerQueryResponse<T>>;
  /** 查詢狀態變更回調 */
  onQueryStateChange?: (queryState: QueryState) => void;
  /** 總資料筆數（在 serverMode 下必須提供） */
  rowCount?: number;

  // 增強功能：支援選擇模式和特定業務邏輯
  /** 是否啟用選擇模式 */
  selectionMode?: boolean;
  /** 選擇變更回調 */
  onSelectionChange?: (selectedItems: T[]) => void;
  /** 當前選中項目 */
  selectedItems?: T[];
  /** 自定義狀態顏色映射函數 */
  statusColorMap?: (record: T) => { [key: string]: string };
  /** 自定義類型顏色映射函數 */
  typeColorMap?: (record: T) => { [key: string]: string };
  /** 移動端卡片頭像渲染函數 */
  avatarRender?: (record: T) => React.ReactNode;
  /** 是否顯示選擇指示器 */
  showSelectionIndicator?: boolean;
}

// 可搜尋多選的下拉面板
const SelectSearchDropdown: React.FC<{
  options: ReadonlyArray<string>;
  placeholder: string;
  selectedKeys: React.Key[];
  setSelectedKeys: (keys: React.Key[]) => void;
  confirm: () => void;
  clearFilters?: () => void;
}> = ({ options, placeholder, selectedKeys, setSelectedKeys, confirm, clearFilters }) => {
  const [localSearch, setLocalSearch] = React.useState('');
  const filtered = React.useMemo(() => {
    const s = localSearch.trim().toLowerCase();
    if (!s) return options as string[];
    return (options as string[]).filter(opt => opt.includes(s));
  }, [options, localSearch]);
  const current = (selectedKeys as string[]) || [];

  return (
    <div style={{ padding: 8, width: 220 }}>
      <Input
        placeholder={placeholder}
        value={localSearch}
        onChange={(e) => setLocalSearch(e.target.value)}
        prefix={<SearchOutlined style={{ color: '#bfbfbf' }} />}
        style={{ marginBottom: 8 }}
        allowClear
      />
      <div style={{ maxHeight: 200, overflowY: 'auto', padding: '4px 0' }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          {filtered.map((opt) => (
            <Checkbox
              key={opt}
              checked={current.includes(opt)}
              onChange={(e) => {
                const checked = e.target.checked;
                const next = checked ? Array.from(new Set([...current, opt])) : current.filter(v => v !== opt);
                setSelectedKeys(next);
              }}
            >
              {opt}
            </Checkbox>
          ))}
        </Space>
      </div>
      <Space style={{ marginTop: 8 }}>
        <Button type="primary" size="small" onClick={() => confirm()} style={{ width: 90 }}>
          套用
        </Button>
        <Button size="small" onClick={() => { clearFilters?.(); confirm(); }} style={{ width: 90 }}>
          重設
        </Button>
      </Space>
    </div>
  );
};

// 統一的過濾動作按鈕（重設在左、套用在右）
const FilterActionButtons: React.FC<{
  confirm: () => void;
  clearFilters?: () => void;
  onApply?: () => void;
}> = ({ confirm, clearFilters, onApply }) => (
  <Space style={{ marginTop: 8 }}>
    <Button
      onClick={() => {
        clearFilters?.();
        confirm();
      }}
      size="small"
      style={{ width: 90 }}
    >
      重設
    </Button>
    <Button type="primary" onClick={() => (onApply ? onApply() : confirm())} icon={<SearchOutlined />} size="small" style={{ width: 90 }}>
      搜尋
    </Button>
  </Space>
);

// 統一的篩選下拉面板（模式驅動）
const UnifiedFilterDropdown: React.FC<{
  mode: 'input' | 'select-search';
  placeholder: string;
  options?: ReadonlyArray<string>;
  selectedKeys: React.Key[];
  setSelectedKeys: (keys: React.Key[]) => void;
  confirm: () => void;
  clearFilters?: () => void;
}> = ({ mode, placeholder, options = [], selectedKeys, setSelectedKeys, confirm, clearFilters }) => {
  if (mode === 'input') {
    return (
      <div style={{ padding: 8 }}>
        <Input
          placeholder={placeholder}
          value={(selectedKeys[0] as string) || ''}
          onChange={(e) => setSelectedKeys(e.target.value ? [e.target.value] : [])}
          onPressEnter={() => confirm()}
          style={{ width: 188, marginBottom: 8, display: 'block' }}
        />
        <FilterActionButtons confirm={confirm} clearFilters={clearFilters} />
      </div>
    );
  }

  // select-search（可搜尋多選）
  const [localSearch, setLocalSearch] = React.useState('');
  const filtered = React.useMemo(() => {
    const s = localSearch.trim().toLowerCase();
    if (!s) return options as string[];
    return (options as string[]).filter(opt => opt.includes(s));
  }, [options, localSearch]);
  const current = (selectedKeys as string[]) || [];

  return (
    <div style={{ padding: 8, width: 220 }}>
      <Input
        placeholder={placeholder}
        value={localSearch}
        onChange={(e) => setLocalSearch(e.target.value)}
        prefix={<SearchOutlined style={{ color: '#bfbfbf' }} />}
        style={{ marginBottom: 8 }}
        allowClear
        onPressEnter={() => {
          const next = filtered;
          setSelectedKeys(next);
          confirm();
        }}
      />
      <div style={{ maxHeight: 200, overflowY: 'auto', padding: '4px 0' }}>
        <Space direction="vertical" style={{ width: '100%' }}>
          {filtered.map((opt) => (
            <Checkbox
              key={opt}
              checked={current.includes(opt)}
              onChange={(e) => {
                const checked = e.target.checked;
                const next = checked ? Array.from(new Set([...current, opt])) : current.filter(v => v !== opt);
                setSelectedKeys(next);
              }}
            >
              {opt}
            </Checkbox>
          ))}
        </Space>
      </div>
      <FilterActionButtons confirm={confirm} clearFilters={clearFilters} />
    </div>
  );
};

/**
 * 默認移動端卡片渲染函數
 * 自動根據列配置生成卡片內容
 */
const defaultMobileCardRender = <T extends Record<string, any>>(
  columns: ColumnsType<T>,
  { record, index, actions }: MobileCardRenderProps<T>
): React.ReactNode => {
  // 過濾掉操作列和不適合移動端顯示的列
  const displayColumns = columns.filter(col =>
    col.key !== 'action' &&
    col.key !== 'actions' &&
    !col.fixed &&
    'dataIndex' in col &&
    col.dataIndex
  );

  return (
    <div>
      <div style={{ marginBottom: 12 }}>
        {/* 主要標題 - 使用第一個非操作列 */}
        {displayColumns[0] && (
          <Typography.Text strong style={{ fontSize: '16px', display: 'block' }}>
            {(() => {
              const col = displayColumns[0] as ColumnType<T>;
              if (typeof col.render === 'function') {
                return col.render(
                  record[col.dataIndex as string],
                  record,
                  index
                );
              }
              return record[col.dataIndex as string] || '-';
            })()}
          </Typography.Text>
        )}
      </div>

      {/* 詳細信息 */}
      <Descriptions size="small" column={1} colon={false}>
        {displayColumns.slice(1).map((col, colIndex) => {
          const column = col as ColumnType<T>;
          const value = record[column.dataIndex as string];
          const renderedValue = typeof column.render === 'function'
            ? column.render(value, record, index)
            : value;

          return (
            <Descriptions.Item
              key={column.key || column.dataIndex as string || colIndex}
              label={column.title as string}
            >
              {renderedValue || '-'}
            </Descriptions.Item>
          );
        })}
      </Descriptions>
    </div>
  );
};

/**
 * 提取操作列的渲染內容
 */
const extractActions = <T extends Record<string, any>>(
  columns: ColumnsType<T>,
  record: T,
  index: number
): React.ReactNode => {
  const actionColumn = columns.find(col =>
    col.key === 'action' ||
    col.key === 'actions' ||
    ('dataIndex' in col && (col.dataIndex === 'action' || col.dataIndex === 'actions'))
  ) as ColumnType<T> | undefined;

  if (!actionColumn || !actionColumn.render) {
    return null;
  }

  return actionColumn.render(record, record, index) as React.ReactNode;
};

/**
 * 創建智能篩選器
 * 根據欄位配置為列添加篩選功能
 */
const createSmartFilter = <T extends Record<string, any>>(
  column: SmartColumnType<T>,
  uniqueValues: ReadonlyArray<string>
): ColumnType<T> => {
  // 檢查是否啟用智能篩選器
  const enabled = !!column.smartFilter;
  if (!enabled) {
    return column;
  }

  // 只對有 dataIndex 且不是操作列的列添加篩選器
  if (!('dataIndex' in column) || !column.dataIndex || 
      column.key === 'action' || column.key === 'actions') {
    return column;
  }

  // 檢查該列是否已經有自定義篩選器
  if (column.filterDropdown || column.filters) {
    return column;
  }

  const filterType: 'text' | 'select' | 'select-search' | 'auto' = (column.filterType as any) || 'auto';
  const placeholder = column.filterPlaceholder || `搜尋${column.title as string}`;

  // 決定模式
  const useInput = filterType === 'text';
  const useSelect = filterType === 'select';
  const useSelectSearch = filterType === 'select-search' || (filterType === 'auto' && uniqueValues.length > 10);

  if (useInput) {
    // 使用輸入框篩選
    return {
      ...column,
      filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }) => (
        <UnifiedFilterDropdown
          mode="input"
          placeholder={placeholder}
          selectedKeys={selectedKeys}
          setSelectedKeys={setSelectedKeys}
          confirm={confirm}
          clearFilters={clearFilters}
        />
      ),
      onFilter: (value, record) => {
        const recordValue = record[column.dataIndex as string];
        return recordValue 
          ? String(recordValue).toLowerCase().includes((value as string).toLowerCase())
          : false;
      },
    };
  } else if (useSelect) {
    // 使用下拉選單篩選（多選）
    return {
      ...column,
      filters: uniqueValues.map(value => ({ text: value, value })),
      filterMultiple: true,
      onFilter: (value, record) => {
        const raw = record[column.dataIndex as string];
        const display = column.filterDisplayValue ? column.filterDisplayValue(raw, record) : raw;
        return display ? String(display).toLowerCase() === (value as string).toLowerCase() : false;
      },
    };
  } else if (useSelectSearch) {
    // 可搜尋的多選清單（含輸入框 + Checkbox 列表）
    return {
      ...column,
      filterDropdown: ({ setSelectedKeys, selectedKeys = [], confirm, clearFilters }) => (
        <UnifiedFilterDropdown
          mode="select-search"
          placeholder={placeholder}
          options={uniqueValues}
          selectedKeys={selectedKeys}
          setSelectedKeys={setSelectedKeys}
          confirm={confirm}
          clearFilters={clearFilters}
        />
      ),
      onFilter: (value, record) => {
        const raw = record[column.dataIndex as string];
        const display = column.filterDisplayValue ? column.filterDisplayValue(raw, record) : raw;
        if (!display) return false;
        // 支援多選：selectedKeys 會是多個值，Antd 會逐一呼叫 onFilter
        return String(display).toLowerCase() === (value as string).toLowerCase();
      }
    };
  }
  // 預設回傳原欄位，避免無回傳造成型別錯誤
  return column;
};

/**
 * 動態篩選器組件
 */
const DynamicTableFilter = <T extends Record<string, any>>({
  filterConfigs,
  dataSource,
  onFilterChange,
  threshold = 10,
  compact = false
}: {
  filterConfigs: Array<{
    dataIndex: string;
    title: string;
    placeholder?: string;
    enabled?: boolean;
  }>;
  dataSource: T[];
  onFilterChange: (filteredData: T[], filterValues: Record<string, string>) => void;
  threshold?: number;
  compact?: boolean;
}) => {
  const [filterValues, setFilterValues] = useState<Record<string, string>>({});
  const debounceRef = React.useRef<number | null>(null);

  const shouldShowFilters = useMemo(() => {
    return dataSource.length > threshold;
  }, [dataSource.length, threshold]);

  const enabledConfigs = useMemo(() => {
    return filterConfigs.filter(config => config.enabled !== false);
  }, [filterConfigs]);

  const filteredData = useMemo(() => {
    if (!shouldShowFilters) {
      return dataSource;
    }

    return dataSource.filter(record => {
      return enabledConfigs.every(config => {
        const filterValue = filterValues[config.dataIndex];
        if (!filterValue) return true;

        const recordValue = record[config.dataIndex as string];
        if (!recordValue) return false;

        return String(recordValue).toLowerCase().includes(filterValue.toLowerCase());
      });
    });
  }, [dataSource, filterValues, enabledConfigs, shouldShowFilters]);

  useEffect(() => {
    // debounce 通知外層，降低頻繁重算
    if (debounceRef.current) {
      window.clearTimeout(debounceRef.current);
    }
    debounceRef.current = window.setTimeout(() => {
      onFilterChange(filteredData, filterValues);
    }, 250);
    return () => {
      if (debounceRef.current) {
        window.clearTimeout(debounceRef.current);
      }
    };
  }, [filteredData, filterValues, onFilterChange]);

  const handleFilterChange = (dataIndex: string, value: string) => {
    setFilterValues(prev => ({ ...prev, [dataIndex]: value }));
  };

  const clearAllFilters = () => {
    setFilterValues({});
  };

  const hasActiveFilters = Object.values(filterValues).some(value => value.trim() !== '');

  if (!shouldShowFilters || enabledConfigs.length === 0) {
    return null;
  }

  return (
    <Card
      size={compact ? 'small' : 'default'}
      style={{
        marginBottom: 16,
        backgroundColor: '#fafafa',
        border: '1px solid #f0f0f0'
      }}
    >
      <div style={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'space-between', 
        marginBottom: compact ? 8 : 12 
      }}>
        <Typography.Text strong style={{ fontSize: compact ? '14px' : '16px' }}>
          快速篩選
        </Typography.Text>
        {hasActiveFilters && (
          <Button
            type="link"
            size="small"
            icon={<ClearOutlined />}
            onClick={clearAllFilters}
            style={{ padding: 0, height: 'auto' }}
          >
            清除篩選
          </Button>
        )}
      </div>

      <Space.Compact style={{ width: '100%' }} size="small">
        {enabledConfigs.map((config, index) => (
          <React.Fragment key={config.dataIndex}>
            <Input
              placeholder={config.placeholder || `搜尋${config.title}`}
              value={filterValues[config.dataIndex] || ''}
              onChange={(e) => handleFilterChange(config.dataIndex, e.target.value)}
              prefix={<SearchOutlined style={{ color: '#bfbfbf' }} />}
              style={{ flex: 1 }}
              size={compact ? 'small' : 'middle'}
            />
            {index < enabledConfigs.length - 1 && (
              <Divider type="vertical" style={{ height: 'auto', margin: '0 8px' }} />
            )}
          </React.Fragment>
        ))}
      </Space.Compact>

      {hasActiveFilters && (
        <div style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
          已篩選出 {filteredData.length} 筆資料（共 {dataSource.length} 筆）
        </div>
      )}
    </Card>
  );
};

const ResponsiveTable = <T extends Record<string, any>>({
  columns = [],
  dataSource = [] as T[],
  mobileCardRender,
  mobilePageSize = 10,
  showMobilePagination = true,
  mobileBreakpoint = 768,
  pagination,
  mobileCardStyle,
  mobileCardClassName = '',
  rowKey = 'key',
  enableDynamicFilter = false,
  filterThreshold = 10,
  filterConfigs = [],
  serverMode = false,
  onServerRequest,
  onQueryStateChange,
  rowCount,
  // 新增的增強功能參數
  selectionMode = false,
  onSelectionChange,
  selectedItems = [],
  statusColorMap,
  typeColorMap,
  avatarRender,
  showSelectionIndicator = true,
  ...tableProps
}: ResponsiveTableProps<T>) => {
  const [isMobile, setIsMobile] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [pageSize, setPageSize] = useState(20);
  const [filteredData, setFilteredData] = useState<T[]>(Array.from(dataSource));
  const [tableFilteredCount, setTableFilteredCount] = useState<number | null>(null);
  
  // 選擇模式狀態管理
  const [internalSelectedItems, setInternalSelectedItems] = useState<T[]>(selectedItems);

  // 同步外部選中項目
  useEffect(() => {
    setInternalSelectedItems(selectedItems);
  }, [selectedItems]);

  // 處理項目選擇
  const handleItemSelection = useCallback((item: T, selected: boolean) => {
    const itemKey = typeof rowKey === 'function' ? rowKey(item) : item[rowKey];
    let newSelection: T[];
    
    if (selected) {
      newSelection = [...internalSelectedItems, item];
    } else {
      newSelection = internalSelectedItems.filter(selectedItem => {
        const selectedKey = typeof rowKey === 'function' ? rowKey(selectedItem) : selectedItem[rowKey];
        return selectedKey !== itemKey;
      });
    }
    
    setInternalSelectedItems(newSelection);
    onSelectionChange?.(newSelection);
  }, [internalSelectedItems, rowKey, onSelectionChange]);

  // 檢查項目是否被選中
  const isItemSelected = useCallback((item: T): boolean => {
    if (!selectionMode) return false;
    const itemKey = typeof rowKey === 'function' ? rowKey(item) : item[rowKey];
    return internalSelectedItems.some(selectedItem => {
      const selectedKey = typeof rowKey === 'function' ? rowKey(selectedItem) : selectedItem[rowKey];
      return selectedKey === itemKey;
    });
  }, [selectionMode, internalSelectedItems, rowKey]);

  // 移動端檢測
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth <= mobileBreakpoint);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);

    return () => {
      window.removeEventListener('resize', checkMobile);
    };
  }, [mobileBreakpoint]);

  // 更新篩選資料當資料源改變時（本地模式）
  useEffect(() => {
    if (!serverMode) {
      setFilteredData(Array.from(dataSource));
    }
  }, [dataSource, serverMode]);

  // 伺服器模式初始化載入
  useEffect(() => {
    if (serverMode && onServerRequest) {
      // 初始載入第一頁資料
      const initialQueryState: QueryState = {
        page: 1,
        pageSize,
        columnFilters: [],
        globalFilters: undefined
      };

      onServerRequest(initialQueryState).then(response => {
        setFilteredData(response.data);
        setTableFilteredCount(response.total);
        setCurrentPage(1);
      }).catch(error => {
        console.error('Server request failed:', error);
        setFilteredData([]);
        setTableFilteredCount(0);
      });
    }
  }, [serverMode, onServerRequest, pageSize]);

  // 一次性建立 per-column unique 值快取，避免每欄重算
  // 在 serverMode 下不建立快取，因為資料是動態載入的
  const columnUniqueMap = useMemo(() => {
    if (serverMode) {
      return new Map<string, string[]>();
    }

    const map = new Map<string, string[]>();
    // 預先規整資料，降低多次 toLowerCase 開銷
    const normalizedRows = filteredData as ReadonlyArray<Record<string, any>>;
    const resolveColumnKey = (c: any): string | null => {
      if (c && c.key) return String(c.key);
      if (c && 'dataIndex' in c && c.dataIndex) return String(c.dataIndex);
      return null;
    };
    columns.forEach(col => {
      const key = resolveColumnKey(col);
      if (!key || (col as any).key === 'action' || (col as any).key === 'actions') return;
      if (!('dataIndex' in (col as any)) || !(col as any).dataIndex) return;
      const values = new Set<string>();
      for (let i = 0; i < normalizedRows.length; i++) {
        const raw = normalizedRows[i][(col as any).dataIndex as string];
        const display = (col as any).filterDisplayValue ? (col as any).filterDisplayValue(raw, normalizedRows[i]) : raw;
        if (display !== undefined && display !== null && display !== '') {
          values.add(String(display).toLowerCase());
        }
      }
      map.set(key, Array.from(values));
    });
    return map;
  }, [columns, filteredData, serverMode]);

  // 智能篩選器處理（使用快取的 unique 值）
  const filteredColumns = useMemo(() => {
    const resolveColumnKey = (c: any): string | null => {
      if (c && c.key) return String(c.key);
      if (c && 'dataIndex' in c && c.dataIndex) return String(c.dataIndex);
      return null;
    };
    return columns.map(col => {
      const withSorterTooltip = (col as any).sorter
        ? { ...col, showSorterTooltip: (col as any).showSorterTooltip ?? false }
        : col;
      const key = resolveColumnKey(col) || '';
      const uniques = columnUniqueMap.get(key) || [];
      return createSmartFilter(withSorterTooltip as SmartColumnType<T>, uniques);
    });
  }, [columns, columnUniqueMap]);

  // 動態篩選器處理
  const handleFilterChange = (filteredData: T[], filterValues: Record<string, string>) => {
    setFilteredData(filteredData);
    setCurrentPage(1); // 重置到第一頁
  };

  // 分頁數據計算
  const paginatedData = useMemo(() => {
    if (!isMobile || pagination === false) {
      return filteredData;
    }

    const start = (currentPage - 1) * mobilePageSize;
    const end = start + mobilePageSize;
    return filteredData.slice(start, end);
  }, [filteredData, currentPage, mobilePageSize, isMobile, pagination]);

  // 移動端列表渲染
  const renderMobileList = () => {
    return (
      <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
        {paginatedData.map((record, index) => {
          const actualIndex = (currentPage - 1) * mobilePageSize + index;
          const actions = extractActions(columns, record, actualIndex);
          const key = typeof rowKey === 'function' ? rowKey(record) : record[rowKey];
          const isSelected = isItemSelected(record);

          const cardContent = mobileCardRender 
            ? mobileCardRender({ record, index: actualIndex, actions })
            : defaultMobileCardRender(filteredColumns, { record, index: actualIndex, actions });

          return (
            <Card
              key={key}
              size="small"
              className={`mobile-item ${mobileCardClassName}`}
              style={{
                marginBottom: 0,
                border: isSelected && showSelectionIndicator ? '2px solid #1890ff' : '1px solid #f0f0f0',
                backgroundColor: isSelected ? '#f0f8ff' : 'transparent',
                ...mobileCardStyle
              }}
              actions={actions ? [actions] : undefined}
              onClick={() => selectionMode && handleItemSelection(record, !isSelected)}
            >
              {/* 選擇指示器 */}
              {selectionMode && showSelectionIndicator && isSelected && (
                <div style={{
                  position: 'absolute',
                  top: 8,
                  right: 8,
                  zIndex: 1,
                  color: '#1890ff',
                  fontSize: '16px'
                }}>
                  <CheckOutlined />
                </div>
              )}
              {cardContent}
            </Card>
          );
        })}

        {/* 移動端分頁 */}
        {showMobilePagination && filteredData.length > mobilePageSize && (
          <div style={{ textAlign: 'center', marginTop: 16 }}>
            <Space>
              <Button
                disabled={currentPage === 1}
                onClick={() => setCurrentPage(currentPage - 1)}
                size="small"
              >
                上一頁
              </Button>
              <span style={{ fontSize: '14px', color: '#666' }}>
                {currentPage} / {Math.ceil(filteredData.length / mobilePageSize)}
              </span>
              <Button
                disabled={currentPage >= Math.ceil(filteredData.length / mobilePageSize)}
                onClick={() => setCurrentPage(currentPage + 1)}
                size="small"
              >
                下一頁
              </Button>
            </Space>
            <div style={{ marginTop: 8, fontSize: '12px', color: '#666' }}>
              共 {filteredData.length} 項，每頁 {mobilePageSize} 項
            </div>
          </div>
        )}
      </div>
    );
  };

  // 桌面端表格渲染
  const renderDesktopTable = () => {
    const finalPagination = pagination === false ? false : {
      ...pagination,
      current: currentPage,
      pageSize: pageSize,
      total: (tableFilteredCount ?? filteredData.length),
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total: number, range: [number, number]) => {
        const t = tableFilteredCount ?? total;
        return `第 ${range[0]}-${range[1]} 項，共 ${t} 項`;
      },
      pageSizeOptions: ['10', '20', '50', '100'],
      onChange: (page: number, size: number) => {
        setCurrentPage(page);
        if (size !== pageSize) {
          setPageSize(size);
          setCurrentPage(1);
        }
      },
      onShowSizeChange: (_: number, size: number) => {
        setPageSize(size);
        setCurrentPage(1);
      }
    };

    return (
      <Table
        {...tableProps}
        columns={filteredColumns}
        dataSource={filteredData}
        rowKey={rowKey}
        pagination={finalPagination}
        scroll={{ x: 'max-content', ...tableProps.scroll }}
        size="middle"
        onChange={(p: TablePaginationConfig, f: Record<string, FilterValue | null>, s: SorterResult<T> | SorterResult<T>[], extra) => {
          if (serverMode && onServerRequest) {
            // 伺服器模式：收集查詢狀態並發送請求
            const queryState: QueryState = {
              page: p.current || 1,
              pageSize: p.pageSize || pageSize,
              sort: s && !Array.isArray(s) && s.field ? {
                field: String(s.field),
                order: s.order
              } : undefined,
              columnFilters: f ? Object.entries(f).map(([dataIndex, filterInfo]) => ({
                dataIndex,
                type: 'auto', // 預設類型，實際使用時需要從 column 配置中獲取
                values: Array.isArray(filterInfo) ? filterInfo.map(v => String(v)) : [],
                text: filterInfo && !Array.isArray(filterInfo) ? String(filterInfo) : undefined
              })) : [],
              globalFilters: undefined // 由 FilterSearchContainer 提供
            };

            // 通知外部查詢狀態變更
            onQueryStateChange?.(queryState);

            // 發送伺服器請求
            onServerRequest(queryState).then(response => {
              setFilteredData(response.data);
              setTableFilteredCount(response.total);
              setCurrentPage(p.current || 1);
              setPageSize(p.pageSize || pageSize);
            }).catch(error => {
              console.error('Server request failed:', error);
            });
          } else {
            // 本地模式：使用原有邏輯
            if (extra && Array.isArray(extra.currentDataSource)) {
              setTableFilteredCount(extra.currentDataSource.length);
            }
            tableProps.onChange?.(p, f, s, extra);
          }
        }}
        onRow={(record) => ({
          onClick: () => selectionMode && handleItemSelection(record, !isItemSelected(record)),
          style: {
            cursor: selectionMode ? 'pointer' : 'default',
            backgroundColor: isItemSelected(record) ? '#f0f8ff' : 'transparent'
          }
        })}
        rowClassName={(_, index) =>
          index % 2 === 0 ? 'table-row-light' : 'table-row-dark'
        }
      />
    );
  };

  return (
    <div>
      {/* 動態篩選器 */}
      {enableDynamicFilter && (
        <DynamicTableFilter
          filterConfigs={filterConfigs}
          dataSource={Array.from(dataSource)}
          onFilterChange={handleFilterChange}
          threshold={filterThreshold}
          compact={isMobile}
        />
      )}
      
      {/* 表格內容 */}
      {isMobile ? renderMobileList() : renderDesktopTable()}
    </div>
  );
};

export default ResponsiveTable;