// 響應式聯絡人管理系統 - 統一入口
export { default as ResponsiveContactManagementPage } from './ResponsiveContactManagementPage';
export { default as ResponsiveContactManagementCenter } from '../shared/ResponsiveContactManagementCenter';
export { default as ContactTable } from './ContactTable';
export { default as ResponsiveContactFormModal } from './ResponsiveContactFormModal';
export { default as FilterSearchContainer } from '../shared/FilterSearchContainer';

// 向後兼容的聯絡人管理標籤頁
export { default as ContactManagementTab } from '../ContactManagementTab';

// 樣式文件
import './ResponsiveContactManagement.css';

// 使用範例
/*
import { ContactProvider } from '@/contexts/ContactContext';
import { 
  ResponsiveContactManagementPage,
  ResponsiveContactManagementCenter,
  ResponsiveContactTable,
  ResponsiveContactFormModal 
} from '@/app/ims/components/contact';

// 完整頁面使用
function ContactManagementPageExample() {
  return (
    <ContactProvider>
      <ResponsiveContactManagementPage
        showStatistics={true}
        showExport={true}
        showImport={true}
      />
    </ContactProvider>
  );
}

// 組件單獨使用
function ContactManagementExample() {
  const [contacts, setContacts] = useState([]);
  const [showForm, setShowForm] = useState(false);

  return (
    <ContactProvider>
      <ResponsiveContactTable
        contacts={contacts}
        onEdit={(contact) => setShowForm(true)}
        onSelect={(contact) => console.log(contact)}
        onDelete={(id) => console.log('delete', id)}
      />
      
      <ResponsiveContactFormModal
        visible={showForm}
        mode="create"
        onClose={() => setShowForm(false)}
        onSubmit={(contact) => console.log(contact)}
      />
    </ContactProvider>
  );
}

// 管理中心使用
function ContactManagementCenterExample() {
  const [showCenter, setShowCenter] = useState(false);

  return (
    <ContactProvider>
      <ResponsiveContactManagementCenter
        visible={showCenter}
        mode="standalone"
        onClose={() => setShowCenter(false)}
        onContactCreate={(contact) => console.log('新增聯絡人:', contact)}
      />
    </ContactProvider>
  );
}
*/
