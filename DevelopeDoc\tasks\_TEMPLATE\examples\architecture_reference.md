# 架構參考文件

## 核心文件位置

### 後端開發準則
- **文件位置**：`FAST_ERP_Backend/DevelopeDoc/DEVELOPMENT_GUIDELINES.md`
- **用途**：後端開發的完整規範和最佳實踐
- **關鍵章節**：
  - 2.1 變數命名規範
  - 2.2 CRUD 方法命名規範
  - 3. 服務層開發規範
  - 4. 控制器開發規範
  - 5. 資料處理規範
  - 6. 安全與效能準則
  - 7. 架構一致性準則

### 路由自動處理機制
- **文件位置**：`FAST_ERP_Backend/Middlewares/ModuleRouteConvention.cs`
- **用途**：了解 API 路由如何自動生成
- **關鍵理解**：
  - 控制器使用 `[Route("api/[controller]")]` 會被自動轉換為 `api/{module}/[controller]`
  - 避免使用 `[Route("api/ims/[controller]")]` 造成重複前綴
  - 模組名稱從控制器命名空間自動提取

### 前端 API 配置
- **文件位置**：`FAST_ERP_Frontend/src/config/api.ts`
- **用途**：前端 API 端點配置，確保前後端路由一致性
- **關鍵理解**：
  - 使用 `MODULE_IMS_URL` 等常數統一管理模組前綴
  - 所有 API 端點都必須與後端實際路由一致
  - 遵循統一的命名規範

## 常見問題與解決方案

### Q1: 為什麼後端控制器使用 `[Route("api/[controller]")]` 而不是 `[Route("api/ims/[controller]")]`？

**A**: 因為 `ModuleRouteConvention.cs` 會自動為 `Controllers/Ims/` 下的控制器添加 `api/ims/` 前綴。如果使用 `[Route("api/ims/[controller]")]`，會產生 `api/ims/ims/PartnerContact` 的重複前綴。

### Q2: 如何確保前後端路由一致性？

**A**: 
1. 後端使用 `[Route("api/[controller]")]` 讓 `ModuleRouteConvention` 自動處理
2. 前端 `api.ts` 使用 `MODULE_IMS_URL` 等常數統一管理
3. 定期檢查前後端路由是否一致

### Q3: 開發時應該遵循哪些規範？

**A**: 必須遵循 `DEVELOPMENT_GUIDELINES.md` 中的所有規範，特別是：
- 變數命名規範
- CRUD 方法命名規範
- API 設計原則
- Transaction 處理規範
- 權限檢查規範

## 檢查清單

### 開發前
- [ ] 是否已閱讀 `DEVELOPMENT_GUIDELINES.md`？
- [ ] 是否了解 `ModuleRouteConvention.cs` 的路由處理機制？
- [ ] 是否檢查了前端 `api.ts` 的路由配置？

### 開發中
- [ ] 是否遵循變數命名規範？
- [ ] 是否使用標準的 CRUD 方法命名？
- [ ] 是否遵循 API 設計原則？
- [ ] 是否正確處理 Transaction？
- [ ] 是否遵循權限檢查規範？

### 開發後
- [ ] 是否通過所有 linter 檢查？
- [ ] 是否通過前後端整合測試？
- [ ] 是否確保資料一致性？
- [ ] 是否更新相關文件？
