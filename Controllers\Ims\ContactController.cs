using FAST_ERP_Backend.Controllers;
using FAST_ERP_Backend.Interfaces.Ims;
using FAST_ERP_Backend.Models.Ims;
using Microsoft.AspNetCore.Mvc;
using Swashbuckle.AspNetCore.Annotations;
using FAST_ERP_Backend.Filters;

namespace FAST_ERP_Backend.Controllers.Ims;

/// <summary> 聯絡人管理 </summary>
[Route("api/[controller]")]
[ApiController]
[SwaggerTag("聯絡人管理")]
public class ContactController(IContactService service) : GenericController<Contact, ContactDTO, IContactService>(service, "聯絡人")
{
    /// <summary> 取得聯絡人列表 </summary>
    [HttpGet]
    [RequirePermission("Ims/Contact", "Read")]
    [SwaggerOperation(Summary = "取得聯絡人列表", Description = "取得所有聯絡人資料")]
    public override async Task<IActionResult> Get()
    {
        return await base.Get();
    }

    /// <summary> 根據ID取得單筆聯絡人 </summary>
    [HttpGet("{id}")]
    [RequirePermission("Ims/Contact", "Read")]
    [SwaggerOperation(Summary = "取得單筆聯絡人", Description = "根據ID取得單筆聯絡人資料")]
    public override async Task<IActionResult> Get([FromRoute] Guid id)
    {
        return await base.Get(id);
    }

    /// <summary> 新增聯絡人 </summary>
    [HttpPost]
    [RequirePermission("Ims/Contact", "Create")]
    [SwaggerOperation(Summary = "新增聯絡人", Description = "新增一筆聯絡人資料")]
    public override async Task<IActionResult> Create([FromBody] ContactDTO dto)
    {
        return await base.Create(dto);
    }

    /// <summary> 更新聯絡人 </summary>
    [HttpPut]
    [RequirePermission("Ims/Contact", "Update")]
    [SwaggerOperation(Summary = "更新聯絡人", Description = "更新一筆聯絡人資料")]
    public override async Task<IActionResult> Update([FromBody] ContactDTO dto)
    {
        return await base.Update(dto);
    }

    /// <summary> 刪除聯絡人 </summary>
    [HttpDelete("{id}")]
    [RequirePermission("Ims/Contact", "Delete")]
    [SwaggerOperation(Summary = "刪除聯絡人", Description = "根據ID刪除一筆聯絡人資料")]
    public override async Task<IActionResult> Delete([FromRoute] Guid id)
    {
        return await base.Delete(id);
    }
}